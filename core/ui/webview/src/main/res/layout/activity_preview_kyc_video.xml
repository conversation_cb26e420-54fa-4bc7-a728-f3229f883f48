<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#e5e5e5">


    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guide"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.40" />

    <ImageView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@drawable/shape_card_video_background"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/guide" />


    <include
        android:id="@+id/include_toolbar"
        layout="@layout/toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@id/ll_progress"/>


    <LinearLayout
        android:id="@+id/ll_progress"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_6dp"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_10dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/include_toolbar">

        <View
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="@drawable/shape_progress" />

        <View
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:layout_marginEnd="10dp"
            android:layout_weight="1"
            android:background="@drawable/shape_progress" />

        <View
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="@drawable/shape_progress" />

        <View
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:layout_marginEnd="10dp"
            android:layout_weight="1"
            android:background="@drawable/shape_progress" />

        <View
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="@drawable/shape_progress" />

        <View
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="10dp"
            android:background="@drawable/shape_progress" />
    </LinearLayout>

    <TextView
        android:id="@+id/tv_title_1"
        style="@style/Heading2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_20dp"
        android:text="@string/konfirmasi_video"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ll_progress" />

    <TextView
        android:id="@+id/tv_subtitle"
        style="@style/Body4"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:text="@string/lib_video_sub_title"
        app:layout_constraintStart_toStartOf="@id/tv_title_1"
        app:layout_constraintTop_toBottomOf="@id/tv_title_1" />

    <TextView
        android:id="@+id/tv_error"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_10dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:background="@drawable/shape_error_upload"
        android:drawablePadding="@dimen/_5dp"
        android:gravity="center_vertical"
        android:padding="@dimen/_10dp"
        android:text="@string/video_upload_error"
        android:textColor="@color/black_60"
        android:textSize="@dimen/text_12sp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_subtitle"
        app:drawableStartCompat="@drawable/lib_ic_error" />

    <RelativeLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="70dp"
        android:layout_marginTop="@dimen/_10dp"
        android:layout_marginEnd="70dp"
        android:layout_marginBottom="10dp"
        android:background="@drawable/shape_video_background"
        android:padding="@dimen/_5dp"
        app:cardCornerRadius="15dp"
        app:layout_constraintBottom_toTopOf="@id/tv_discription1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_error">

        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:foregroundGravity=""
            app:cardCornerRadius="10dp">

            <VideoView
                android:id="@+id/videoView"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content" />

            <ImageView
                android:id="@+id/iv_play"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:layout_gravity="center"
                android:background="@drawable/ic_play_video"
                android:contentDescription="" />

        </androidx.cardview.widget.CardView>


    </RelativeLayout>

    <TextView
        android:id="@+id/tv_discription1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        style="@style/Body1"
        android:textStyle="bold"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginBottom="@dimen/_10dp"
        android:text="@string/lib_video_description1"
        app:layout_constraintBottom_toTopOf="@id/tv_discription"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <TextView
        android:id="@+id/tv_discription"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_10dp"
        android:layout_marginBottom="@dimen/_10dp"
        android:text="@string/lib_video_description2"
        android:textColor="@color/black_40"
        android:textSize="@dimen/text_12sp"
        app:layout_constraintBottom_toTopOf="@id/cv_card_secure"
        app:layout_constraintStart_toStartOf="@id/tv_title_1" />


    <androidx.cardview.widget.CardView
        android:id="@+id/cv_card_secure"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:layout_marginBottom="@dimen/_20dp"
        app:cardCornerRadius="@dimen/_5dp"
        app:layout_constraintBottom_toTopOf="@id/btn_retake"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/lib_black_0"
            android:padding="@dimen/_10dp">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/iv_secure"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="5dp"
                android:src="@drawable/ic_lib_security"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_secure_1"
                style="@style/SubHeading2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_10dp"
                android:text="@string/lib_data_security_desc"
                app:layout_constraintStart_toEndOf="@+id/iv_secure"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_secure_2"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:text="@string/lib_data_security"
                android:textColor="@color/black_40"
                android:textSize="@dimen/text_10sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="@id/tv_secure_1"
                app:layout_constraintTop_toBottomOf="@id/tv_secure_1" />

                  </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.cardview.widget.CardView>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_retake"
        style="@style/ButtonOutline.Yellow"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_14dp"
        android:layout_marginEnd="4dp"
        android:text="@string/lib_ambil_ulang"
        android:textAllCaps="false"
        android:textAppearance="@style/SubHeading1"
        app:layout_constraintBottom_toBottomOf="@+id/btn_send"
        app:layout_constraintEnd_toStartOf="@id/btn_send"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/btn_send" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_send"
        style="@style/ButtonOutline"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="4dp"
        android:layout_marginTop="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_14dp"
        android:layout_marginBottom="@dimen/_18dp"
        android:backgroundTint="@color/lib_new_yellow"
        android:text="@string/lib_kirim"
        android:textAllCaps="false"
        android:textAppearance="@style/SubHeading1"
        android:textColor="@color/black_80"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/btn_retake" />

  </androidx.constraintlayout.widget.ConstraintLayout>