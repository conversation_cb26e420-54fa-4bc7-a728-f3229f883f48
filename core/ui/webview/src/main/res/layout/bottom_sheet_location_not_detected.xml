<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:padding="@dimen/_16dp">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_location_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:srcCompat="@drawable/ic_location_denied"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <TextView
        android:id="@+id/tv_location_not_detected"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/iv_location_icon"
        android:layout_marginTop="@dimen/_16dp"
        android:text="@string/lib_location_not_detected"
        style="@style/Heading3"/>

    <TextView
        android:id="@+id/tv_enable_location_access"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_location_not_detected"
        android:layout_marginTop="@dimen/_16dp"
        android:text="@string/lib_enable_location_access"
        style="@style/Body3"/>
    
    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_find_location"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/tv_enable_location_access"
        android:layout_marginTop="@dimen/_16dp"
        style="@style/ButtonOutline.Blue"
        android:text="@string/lib_find_location"/>



</androidx.constraintlayout.widget.ConstraintLayout>