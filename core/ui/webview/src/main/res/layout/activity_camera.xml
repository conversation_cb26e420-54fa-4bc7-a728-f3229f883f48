<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.camera.view.PreviewView
        android:id="@+id/preview_camera"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_image_preview"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_image_preview"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/black_80_50p"
            android:padding="@dimen/_18dp"
            app:layout_constraintBottom_toBottomOf="parent">

            <TextView
                android:id="@+id/btn_retake"
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/_6dp"
                android:background="@drawable/bg_corner_4dp_stroke_white"
                android:gravity="center"
                android:padding="@dimen/_12dp"
                android:text="@string/lib_retake_photo"
                android:textAllCaps="false"
                android:textAppearance="@style/Heading3"
                android:textColor="@color/white"
                app:layout_constraintBottom_toBottomOf="@+id/btn_continue"
                app:layout_constraintEnd_toStartOf="@id/btn_continue"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@+id/btn_continue" />

            <TextView
                android:id="@+id/btn_continue"
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_6dp"
                android:layout_marginTop="@dimen/_16dp"
                android:background="@drawable/bg_solid_white_corner_4dp"
                android:gravity="center"
                android:padding="@dimen/_12dp"
                android:text="@string/lib_continue"
                android:textAllCaps="false"
                android:textAppearance="@style/Heading3"
                android:textColor="@color/black_80"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/btn_retake" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.bukuwarung.lib.webview.camera.overlay.OverlayView
        android:id="@+id/overlay_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@android:color/transparent"
        app:contentInsetLeft="0dp"
        app:contentInsetStart="0dp"
        app:contentInsetStartWithNavigation="0dp"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/backBtn"
                android:layout_width="25dp"
                android:layout_height="25dp"
                android:layout_marginLeft="@dimen/_8dp"
                android:gravity="center"
                android:src="@mipmap/back_white"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="24dp"
                android:ellipsize="end"
                android:maxLines="1"
                android:textAppearance="@style/TextAppearance.AppCompat.Medium"
                android:textColor="@color/white"
                android:textFontWeight="700"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/help_txt"
                app:layout_constraintStart_toEndOf="@id/backBtn"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/help_txt"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/_12dp"
                android:text="@string/lib_help_bantuan"
                android:textColor="@color/white"
                app:drawableTopCompat="@drawable/ic_baseline_help_outline_white"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.appcompat.widget.Toolbar>

    <TextView
        android:id="@+id/tv_error"
        style="@style/Body2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:layout_marginBottom="@dimen/_20dp"
        android:gravity="center"
        android:text="@string/lib_photo_too_dark_increase_brightness"
        android:textColor="@color/red_80"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/guideline_txt"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <TextView
        android:id="@+id/guideline_txt"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_20dp"
        android:layout_marginEnd="@dimen/_20dp"
        android:layout_marginBottom="@dimen/_7dp"
        android:gravity="center"
        android:lineSpacingExtra="6sp"
        android:textColor="@color/white"
        android:textFontWeight="700"
        android:textSize="@dimen/text_14sp"
        app:layout_constraintBottom_toTopOf="@id/camera_capture_btn"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <ImageView
        android:id="@+id/flash_btn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:elevation="2dp"
        android:src="@drawable/ic_flash_off"
        app:layout_constraintBottom_toBottomOf="@id/camera_capture_btn"
        app:layout_constraintEnd_toStartOf="@id/camera_capture_btn"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/camera_capture_btn" />

    <ImageView
        android:id="@+id/camera_capture_btn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/_10dp"
        android:elevation="2dp"
        android:src="@drawable/ic_capture"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="45dp"
        android:layout_height="45dp"
        android:layout_marginBottom="@dimen/_8dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/camera_capture_btn"
        app:layout_constraintEnd_toEndOf="@id/camera_capture_btn"
        app:layout_constraintStart_toStartOf="@id/camera_capture_btn"
        app:layout_constraintTop_toTopOf="@id/camera_capture_btn" />

    <ImageView
        android:id="@+id/switch_btn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:elevation="2dp"
        android:src="@drawable/ic_camera_switch"
        app:layout_constraintBottom_toBottomOf="@id/camera_capture_btn"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/camera_capture_btn"
        app:layout_constraintTop_toTopOf="@id/camera_capture_btn" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/gr_image_capturing"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="preview_camera, overlay_view, guideline_txt, flash_btn, camera_capture_btn, switch_btn" />

</androidx.constraintlayout.widget.ConstraintLayout>
