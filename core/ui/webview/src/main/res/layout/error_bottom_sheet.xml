<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_top_rounded_corner"
    android:paddingBottom="@dimen/_20dp">

    <ImageView
        android:id="@+id/iv_close"
        android:layout_width="@dimen/_14dp"
        android:layout_height="@dimen/_14dp"
        android:layout_marginTop="@dimen/_20dp"
        android:layout_marginEnd="20dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/close" />

    <ImageView
        android:id="@+id/iv_ic_payment_down"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/iv_close"
        app:srcCompat="@drawable/ic_no_inet" />

    <TextView
        android:id="@+id/tv_payment_down_title"
        style="@style/Heading3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_12dp"
        android:text="@string/lib_inet_lost"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/iv_ic_payment_down" />

    <TextView
        android:id="@+id/tv_payment_down_body"
        style="@style/Body2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_40dp"
        android:layout_marginTop="@dimen/_12dp"
        android:layout_marginEnd="@dimen/_40dp"
        android:gravity="center"
        android:text="@string/lib_no_connection_message"
        android:textAlignment="center"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_payment_down_title" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_payment_down"
        style="@style/ButtonOutline.Blue"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:paddingTop="12dp"
        android:text="@string/lib_reload"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_payment_down_body"
        app:strokeWidth="1dp" />

</androidx.constraintlayout.widget.ConstraintLayout>