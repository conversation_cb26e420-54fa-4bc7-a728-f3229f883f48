<?xml version="1.0" encoding="utf-8"?>

<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    app:cardCornerRadius="@dimen/_20dp"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/_30dp">

        <ImageView
            android:id="@+id/im_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_20dp"
            android:src="@drawable/ic_scanning_video"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_title_1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            style="@style/Heading3"
            android:layout_marginTop="@dimen/_30dp"
            android:gravity="center"
            android:text="@string/video_kamu_sedang_diproses"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/im_icon" />

        <TextView
            android:id="@+id/tv_subtitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            style="@style/SubHeading2"
            android:layout_marginStart="@dimen/_30dp"
            android:layout_marginTop="@dimen/_12dp"
            android:layout_marginEnd="@dimen/_30dp"
            android:layout_marginBottom="@dimen/_30dp"
            android:gravity="center"
            android:text="@string/anlyse_sub_heading"
            android:textColor="@color/black_sub_heading"
            android:textSize="@dimen/text_12sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_title_1" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.cardview.widget.CardView>