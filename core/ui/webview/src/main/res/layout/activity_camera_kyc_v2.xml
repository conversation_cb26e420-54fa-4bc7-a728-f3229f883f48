<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black_0">

    <include
        android:id="@+id/include_toolbar"
        layout="@layout/toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guide"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.5" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/_0dp"
        android:background="@drawable/lib_ic_rectangle"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/guide" />

    <androidx.cardview.widget.CardView
        android:id="@+id/cv_cameraWrapper"
        android:layout_width="330dp"
        android:layout_height="330dp"
        android:foreground="@drawable/lib_ic_circle_capture"
        android:padding="-20dp"
        app:cardCornerRadius="360dp"
        app:cardElevation="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.camera.view.PreviewView
            android:id="@+id/preview_camera"
            android:layout_width="330dp"
            android:layout_height="330dp" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_preview"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone" />

        <ProgressBar
            android:id="@+id/progress_bar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:visibility="gone" />

    </androidx.cardview.widget.CardView>

    <TextView
        android:id="@+id/tv_selfie_title"
        style="@style/Heading2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:layout_marginTop="@dimen/_16dp"
        android:text="@string/lib_liveliness_selfie_title"
        android:textColor="@color/black_80"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/include_toolbar" />

    <TextView
        android:id="@+id/tv_description"
        style="@style/Body2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:layout_marginTop="@dimen/_6dp"
        android:text="@string/lib_liveliness_selfie_description"
        android:textColor="@color/black_60"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_selfie_title" />

    <TextView
        android:id="@+id/tv_confirmation_title"
        style="@style/Heading2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:layout_marginTop="@dimen/_16dp"
        android:text="@string/lib_is_photo_clear"
        android:textColor="@color/black_80"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/tv_confirmation_hint"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <TextView
        android:id="@+id/tv_confirmation_hint"
        style="@style/Body2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:layout_marginBottom="@dimen/_20dp"
        android:text="@string/lib_retake_photo_hint"
        android:textColor="@color/black_80"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/br_bottom_buttons"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/gr_selfie_confirmation"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="tv_confirmation_title, tv_confirmation_hint" />

    <TextView
        android:id="@+id/tv_error"
        style="@style/Body2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:layout_marginBottom="@dimen/_20dp"
        android:gravity="center"
        android:text="@string/lib_photo_too_dark_increase_brightness"
        android:textColor="@color/red_80"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/br_bottom_buttons"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <TextView
        android:id="@+id/tv_processing"
        style="@style/Heading3"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:layout_marginBottom="@dimen/_20dp"
        android:gravity="center"
        android:text="@string/lib_image_processing"
        android:textColor="@color/colorPrimary"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/br_bottom_buttons"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/br_bottom_buttons"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:barrierDirection="top"
        app:constraint_referenced_ids="btn_camera_upload, btn_camera_retake, btn_camera_capture" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_camera_capture"
        style="@style/ButtonFill"
        android:enabled="false"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_20dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:layout_marginBottom="@dimen/_16dp"
        android:padding="@dimen/_12dp"
        android:text="@string/lib_take_pic"
        android:textAllCaps="false"
        app:cornerRadius="@dimen/_4dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:rippleColor="@color/black_40" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_camera_retake"
        style="@style/ButtonOutline"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_20dp"
        android:layout_marginEnd="@dimen/_4dp"
        android:layout_marginBottom="@dimen/_16dp"
        android:padding="@dimen/_12dp"
        android:text="@string/lib_retake_photo"
        android:textAllCaps="false"
        android:textColor="@color/black_80"
        app:cornerRadius="@dimen/_4dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/btn_camera_upload"
        app:layout_constraintStart_toStartOf="parent"
        app:rippleColor="@color/black_40"
        app:strokeColor="@color/black_80" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_camera_upload"
        style="@style/ButtonFill"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_4dp"
        android:layout_marginTop="@dimen/_20dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:layout_marginBottom="@dimen/_16dp"
        android:padding="@dimen/_12dp"
        android:text="@string/lib_kirim"
        android:textAllCaps="false"
        app:cornerRadius="@dimen/_4dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/btn_camera_retake"
        app:rippleColor="@color/black_40" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/gr_camera_actions"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="btn_camera_retake, btn_camera_upload" />

</androidx.constraintlayout.widget.ConstraintLayout>