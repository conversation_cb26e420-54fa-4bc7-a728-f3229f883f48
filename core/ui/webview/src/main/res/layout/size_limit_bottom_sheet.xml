<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_top_rounded_corner"
    android:padding="@dimen/_14dp">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_size_limit"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/ic_size_limit" />

    <TextView
        android:id="@+id/tv_size_limit_title"
        style="@style/Heading3"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_40dp"
        android:layout_marginTop="@dimen/_14dp"
        android:gravity="center"
        android:text="@string/lib_photo_size_too_big"
        android:textColor="@color/black_80"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/iv_size_limit" />

    <TextView
        android:id="@+id/tv_size_limit_description"
        style="@style/Body2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_40dp"
        android:layout_marginTop="@dimen/_14dp"
        android:gravity="center"
        android:text="@string/lib_size_limit_description"
        android:textColor="@color/black_60"
        app:layout_constraintTop_toBottomOf="@+id/tv_size_limit_title" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_size_limit_action"
        style="@style/ButtonFill"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_14dp"
        android:text="@string/lib_retake_photo"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_size_limit_description"
        app:strokeWidth="1dp" />

</androidx.constraintlayout.widget.ConstraintLayout>