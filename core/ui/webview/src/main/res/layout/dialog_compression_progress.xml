<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:cardCornerRadius="@dimen/_10dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="@dimen/_10dp">

        <TextView
            android:id="@+id/tvTitle"
            style="@style/SubHeading2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/upload_berhasil"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <ProgressBar
            android:id="@+id/progress_horizontal"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_10dp"
            android:max="100"
            android:paddingTop="@dimen/_16dp"
            android:paddingBottom="@dimen/_10dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/tvTitle" />

        <TextView
            android:id="@+id/progress"
            style="@style/Body3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_8dp"
            app:layout_constraintBottom_toBottomOf="@id/tvTitle"
            app:layout_constraintStart_toEndOf="@id/tvTitle"
            app:layout_constraintTop_toTopOf="@id/tvTitle"
            tools:text="25%" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_upload_success"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_marginStart="@dimen/_8dp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/tvTitle"
            app:layout_constraintStart_toEndOf="@id/tvTitle"
            app:layout_constraintTop_toTopOf="@id/tvTitle"
            app:srcCompat="@drawable/ic_check_circle_green_liveliness" />


    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.cardview.widget.CardView>