<?xml version="1.0" encoding="utf-8"?>
<resources>
    <declare-styleable name="OverlayView">
        <attr name="srcLine" format="reference" />
        <attr name="srcFull" format="reference" />
    </declare-styleable>
    <style name="BottomSheetDialogTheme" parent="Theme.Design.Light.BottomSheetDialog">
        <item name="backgroundColor">@android:color/transparent</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowSoftInputMode">adjustResize</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="colorAccent">@color/colorPrimary</item>
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">#0091ff</item>
    </style>
    <style name="BottomSheetModal.RoundCorner" parent="Widget.Design.BottomSheet.Modal">
        <item name="android:background">@drawable/bg_top_rounded_corner</item>
    </style>

    <style name="BottomSheetDialogTheme.RoundCorner">
        <item name="bottomSheetStyle">@style/BottomSheetModal.RoundCorner</item>
    </style>

    <style name="ButtonFill" parent="Widget.MaterialComponents.Button.TextButton.Icon">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:paddingBottom">11dp</item>
        <item name="android:paddingTop">11dp</item>
        <item name="android:paddingStart">16dp</item>
        <item name="android:paddingEnd">16dp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:textAppearance">@style/Heading3</item>
        <item name="android:textColor">@color/btn_text_color_state</item>
        <item name="backgroundTint">@color/btn_color_state</item>
        <item name="iconGravity">textStart</item>
        <item name="iconTint">@null</item>
    </style>
    <style name="ButtonOutline" parent="Widget.MaterialComponents.Button.OutlinedButton.Icon">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:paddingBottom">11dp</item>
        <item name="android:paddingTop">11dp</item>
        <item name="android:paddingStart">16dp</item>
        <item name="android:paddingEnd">16dp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:textAppearance">@style/Heading3</item>
        <item name="android:textColor">@color/web_orange</item>
        <item name="strokeColor">@color/web_orange</item>
        <item name="cornerRadius">4dp</item>
        <item name="iconGravity">textStart</item>
    </style>

    <style name="ButtonOutline.Blue" parent="ButtonOutline">
        <item name="android:textColor">@color/colorPrimary</item>
        <item name="android:textAppearance">@style/SubHeading1</item>
        <item name="strokeColor">@color/colorPrimary</item>
    </style>

    <style name="ButtonOutline.Yellow" parent="ButtonOutline">
        <item name="android:textColor">@color/lib_new_yellow</item>
        <item name="android:textAppearance">@style/SubHeading1</item>
        <item name="strokeColor">@color/lib_new_yellow</item>
    </style>

    <style name="BaseTextView" parent="Widget.AppCompat.TextView">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:lineSpacingExtra">6sp</item>
        <item name="android:textColor">@color/black_80</item>
    </style>

    <style name="BaseHeading" parent="BaseTextView">
        <item name="fontWeight">700</item>
    </style>

    <style name="Heading1" parent="BaseHeading">
        <item name="android:textSize">@dimen/text_24sp</item>
        <item name="android:lineSpacingExtra">8sp</item>
    </style>

    <style name="Heading2" parent="BaseHeading">
        <item name="android:textSize">@dimen/text_18sp</item>
    </style>

    <style name="Heading3" parent="BaseHeading">
        <item name="android:textSize">@dimen/text_16sp</item>
    </style>

    <style name="SubHeading1" parent="BaseHeading">
        <item name="android:textSize">@dimen/text_14sp</item>
    </style>

    <style name="SubHeading2" parent="BaseHeading">
        <item name="android:textSize">@dimen/text_12sp</item>
    </style>

    <style name="Body1" parent="BaseTextView">
        <item name="android:textSize">@dimen/text_16sp</item>
    </style>

    <style name="Body2" parent="BaseTextView">
        <item name="android:textSize">@dimen/text_14sp</item>
    </style>

    <style name="Body3" parent="BaseTextView">
        <item name="android:textSize">@dimen/text_12sp</item>
    </style>

    <style name="Body4" parent="Body3">
        <item name="android:textColor">@color/black_40</item>
    </style>

    <style name="Body5" parent="Body3">
        <item name="android:textColor">@color/black_60</item>
    </style>

    <style name="Label1" parent="BaseTextView">
        <item name="android:textSize">@dimen/text_12sp</item>
    </style>
</resources>