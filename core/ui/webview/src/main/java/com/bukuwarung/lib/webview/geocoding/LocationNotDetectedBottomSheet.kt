package com.bukuwarung.lib.webview.geocoding

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.bukuwarung.lib.webview.R
import com.bukuwarung.lib.webview.bottomsheet.BaseBottomSheetDialogFragment
import com.bukuwarung.lib.webview.databinding.BottomSheetLocationNotDetectedBinding

class LocationNotDetectedBottomSheet: BaseBottomSheetDialogFragment() {

    private var _binding: BottomSheetLocationNotDetectedBinding? = null
    private val binding get() = _binding!!
    private var iCommunicator: ICommunicator? = null

    enum class UseCase {
        LOCATION_NOT_DETECTED, LOCATION_OUT_OF_RANGE,
        LOCATION_NOT_DETECTED_PHYSICAL_VISIT
    }

    companion object {
        const val TAG = "location_not_detected_bottom_sheet"
        const val USE_CASE = "use_case"

        fun createIntent(
            isCancellable: Boolean? = false,
            useCase: UseCase = UseCase.LOCATION_NOT_DETECTED
        ) = LocationNotDetectedBottomSheet().apply {
            isCancelable = isCancellable == true
            arguments = Bundle().apply {
                putSerializable(USE_CASE, useCase)
            }
        }
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        iCommunicator = context as? ICommunicator
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = BottomSheetLocationNotDetectedBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        val useCase = arguments?.getSerializable(USE_CASE) as UseCase? ?: UseCase.LOCATION_NOT_DETECTED
        with(binding) {
            btnFindLocation.setOnClickListener {
                iCommunicator?.handleBottomSheetClick(useCase)
                dismiss()
            }

            when (useCase) {
                UseCase.LOCATION_NOT_DETECTED_PHYSICAL_VISIT -> {
                    tvEnableLocationAccess.text = getString(R.string.aw_enable_location_access_pv)
                    btnFindLocation.text = getString(R.string.lib_back)
                }
                UseCase.LOCATION_OUT_OF_RANGE -> {
                    tvLocationNotDetected.text = getString(R.string.aw_location_out_of_range_title)
                    tvEnableLocationAccess.text = getString(R.string.aw_location_out_of_range_message)
                    btnFindLocation.text = getString(R.string.lib_back)
                }
                else -> {}
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    interface ICommunicator {
        fun handleBottomSheetClick(useCase: UseCase)
    }
}