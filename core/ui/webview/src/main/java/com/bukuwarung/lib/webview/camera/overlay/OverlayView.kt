package com.bukuwarung.lib.webview.camera.overlay

import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.util.DisplayMetrics
import android.util.TypedValue
import android.view.View
import androidx.core.content.ContextCompat
import com.bukuwarung.lib.webview.R
import com.bukuwarung.lib.webview.util.Constant.TYPE_BANK
import com.bukuwarung.lib.webview.util.Constant.TYPE_BPJS
import com.bukuwarung.lib.webview.util.Constant.TYPE_FAMILY_PHOTO
import com.bukuwarung.lib.webview.util.Constant.TYPE_KTP
import com.bukuwarung.lib.webview.util.Constant.TYPE_KYB_INVENTORY
import com.bukuwarung.lib.webview.util.Constant.TYPE_KYB_STORE
import com.bukuwarung.lib.webview.util.Constant.TYPE_NPWP
import com.bukuwarung.lib.webview.util.Constant.TYPE_PHYSICAL_VISIT_STORE
import com.bukuwarung.lib.webview.util.Constant.TYPE_SELFIE_KTP

class OverlayView : View {

    private var bitmapLine: Bitmap? = null
    private var bitmapFull: Bitmap? = null
    private val appBarHeight = 60
    private val bottomIconHeight = 165
    var topImage = 0
    var leftImage = 0
    var widthImage = 0
    var heightImage = 0
    private val paint = Paint(Paint.ANTI_ALIAS_FLAG or Paint.FILTER_BITMAP_FLAG)

    constructor(context: Context?) : super(context)

    constructor(context: Context?, attrs: AttributeSet?) : super(context, attrs)

    constructor(context: Context?, attrs: AttributeSet?, defStyleAttr: Int) : super(context, attrs, defStyleAttr)

    fun setCameraType(type: String) {
        when(type) {
            TYPE_KTP, TYPE_BPJS, TYPE_NPWP -> {
                bitmapLine = BitmapFactory.decodeResource(resources, R.drawable.card_overlay)
                bitmapFull = BitmapFactory.decodeResource(resources, R.drawable.card_overlay_full)
            }
            TYPE_FAMILY_PHOTO, TYPE_BANK, TYPE_KYB_STORE, TYPE_KYB_INVENTORY, TYPE_PHYSICAL_VISIT_STORE -> {
                bitmapLine = BitmapFactory.decodeResource(resources, R.drawable.photo_16_9)
                bitmapFull = BitmapFactory.decodeResource(resources, R.drawable.photo_16_9_full)
            }
            else -> {
                bitmapLine = BitmapFactory.decodeResource(resources, R.drawable.selfie_area)
                bitmapFull = BitmapFactory.decodeResource(resources, R.drawable.selfie_area_full)
            }
        }
        bitmapFull?.run {
            val metrics: DisplayMetrics = context.resources.displayMetrics
            val w: Int = metrics.widthPixels
            val h: Int = metrics.heightPixels
            widthImage = width
            heightImage = height
            leftImage = w / 2 - width / 2
            if (type == TYPE_SELFIE_KTP) {
                topImage = h * 3 / 8 - height / 2
            } else if (type == TYPE_FAMILY_PHOTO || type == TYPE_KYB_INVENTORY || type == TYPE_KYB_STORE || type == TYPE_PHYSICAL_VISIT_STORE) {
                topImage =  getAppBarHeight(h,height)
            } else topImage = h / 3 - height / 2
        }
        invalidate()
    }

    private fun getAppBarHeight(hDevice:Int, hFrame:Int):Int{
        val tv = TypedValue()
        val topImage =  if (context.theme.resolveAttribute(android.R.attr.actionBarSize, tv, true)) {
            TypedValue.complexToDimensionPixelSize(tv.data, resources.displayMetrics)
        }else{
            getPixel(appBarHeight)
        }
        val area = hDevice - (topImage + getPixel(bottomIconHeight))
        return if(hFrame> area)
            topImage
        else
            topImage + (area/2 - hFrame/2)

    }

    private fun getPixel(dp:Int):Int{
        return (dp * context.resources.displayMetrics.density).toInt()
    }


    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        if (bitmapLine != null) {
            canvas.drawBitmap(bitmapLine!!, leftImage.toFloat(), topImage.toFloat(), paint)
        }
        if (bitmapFull != null) {
            canvas.drawBitmap(bitmapOverlay(bitmapFull!!), 0f, 0f, paint)
        }
    }

    private fun bitmapOverlay(bitmapFull: Bitmap): Bitmap {
        val metrics: DisplayMetrics = context.resources.displayMetrics
        val w: Int = metrics.widthPixels
        val h: Int = metrics.heightPixels
        val bmOverlay = Bitmap.createBitmap(w, h, bitmapFull.config)
        val canvas = Canvas(bmOverlay)
        canvas.drawBitmap(bitmapFull, leftImage.toFloat(), topImage.toFloat(), paint)
        canvas.drawColor(ContextCompat.getColor(context, R.color.black_overlay), PorterDuff.Mode.SRC_OUT)
        return bmOverlay
    }
}
