package com.bukuwarung.lib.webview

import android.app.Activity
import android.content.Intent
import android.graphics.Bitmap
import android.net.Uri
import android.view.View
import android.webkit.WebResourceRequest
import android.webkit.WebResourceResponse
import android.webkit.WebView
import android.webkit.WebViewClient
import com.bukuwarung.lib.webview.util.Utils
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.Response
import java.io.IOException


class DefaultWebViewClient(private val activity: Activity, private val deepLinkScheme: String? = null,
                           private val token: String? = null, private val userId: String? = null,
                           private val appsflyerId: String? = null,
private val listener: WebViewClientListener? = null) : WebViewClient() {
    interface WebViewClientListener {
        fun onUrlChange(url: String?)
    }

    override fun onPageStarted(view: WebView?, url: String?, favicon: Bitmap?) {
        super.onPageStarted(view, url, favicon)
        activity?.runOnUiThread {
            listener?.onUrlChange(url)
        }
    }
    override fun shouldInterceptRequest(view: WebView?, url: String?): WebResourceResponse? {
        token ?: return super.shouldInterceptRequest(view, url)
        userId ?: return super.shouldInterceptRequest(view, url)

        if (!Utils.isBwUrl(url)) return super.shouldInterceptRequest(view, url)
        try {
            val httpClient = OkHttpClient()
            val request: Request = Request.Builder()
                    .url(url!!.trim())
                    .addHeader("Authorization", "Bearer $token")
                    .addHeader("ownerId", userId)
                    .addHeader("appsflyerId", appsflyerId ?: "")
                    .build()

            val response: Response = httpClient.newCall(request).execute()

            return WebResourceResponse(
                    null,
                    response.header("content-encoding", "utf-8"),
                    response.body?.byteStream()
            )
        } catch (e: IOException) {
            return super.shouldInterceptRequest(view, url)
        }
    }

    override fun shouldOverrideUrlLoading(view: WebView?, request: WebResourceRequest?): Boolean {
        try {
            val intent: Intent
            var url = request?.url?.toString()
            url ?: throw IllegalStateException()
            return if (deepLinkScheme != null && url.contains(deepLinkScheme)) {
                intent = Intent(Intent.ACTION_VIEW)
                url = url.replace("intent://", "https://")
                intent.data = Uri.parse(url)
                intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
                activity.startActivity(intent)
                activity.finish()
                true
            } else if (!url.startsWith("https://") && !url.startsWith("http://")) {
                // for redirection like shopeepay
                intent = Intent()
                intent.action = Intent.ACTION_VIEW
                intent.data = Uri.parse(url)
                if (intent.resolveActivity(activity.packageManager) != null) {
                    view?.visibility = View.GONE
                    activity.startActivity(intent)
                    activity.finish()
                }
                true
            } else {
                // do not contains app deeplink
                false
            }
        } catch (ex: Exception) {
//            activity?.let { NotificationUtils.alertToastWithContext(it, "Tidak dapat membuka link!") }
            return false
        }
    }

}
