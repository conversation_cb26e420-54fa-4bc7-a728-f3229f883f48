package com.bukuwarung.lib.webview.util

import android.content.Context
import android.content.Intent
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.google.gson.Gson
import com.google.gson.JsonObject
import org.json.JSONObject

object AnalyticsUtil {
    const val EVENT_SELFIE_CONFIRM_CLICKED = "selfie_confirm_clicked"
    const val EVENT_SELFIE_UPLOAD_COMPLETED = "selfie_upload_completed"
    const val EVENT_KYC_VIDEO_SUBMIT_CLICKED = "kyc_video_submit_clicked"
    const val EVENT_KYB_TAKE_STORE_PHOTO_CLICKED = "kyb_take_store_photo_clicked"
    const val EVENT_REQUEST_PHY_VER_LOCATION = "request_physical_verification_location"
    const val EVENT_VALIDATION_ERROR = "validation_error"

    const val SUBMIT_KYC = "submit_kyc"
    const val ADD_ADDITIONAL_DOCS = "add_additional_docs"
    const val LENDING = "lending"
    const val BNPL_REGISTRATION_COMMERCE = "bnpl_registration_commerce"
    const val BNPL_REGISTRATION_PPOB = "bnpl_registration_ppob"
    const val BNPL = "bnpl"
    const val NEXT_PAGE = "next_page"
    const val SUBMISSION_NUMBER = "submission_number"
    const val STATUS = "status"
    const val SUCCESS = "success"
    const val FAILED = "failed"
    const val REASON = "reason"
    const val YES = "YES"
    const val NO = "NO"
    const val GET_LOCATION_PERMISSION = "get_location_permission"
    const val GET_LOCATION = "get_location"
    const val GET_LOCATION_FAILED_REASON = "get_location_failed_reason"
    const val GET_LOCATION_TIME_IN_SECONDS = "get_location_time_in_seconds"
    const val CITY_IN_SALES_AREA = "city_in_sales_area"
    const val CITY_LOCATION = "city_location"
    const val ADDRESS = "address"
    const val ERROR_TYPE = "error_type"
    const val ENTRY_POINT = "entry_point"
    const val KYB = "kyb"
    const val PHYSICAL_VISIT = "physical_visit"
    const val USE_CASE = "use_case"
    const val KYC_PROVIDER_MISMATCH = "kyc_provider_mismatch"
    const val PERMISSION_DENIED = "permission_denied"
    const val GPS_DISABLED = "gps_disabled"

    fun getJsonStringOfMap(map: MutableMap<String?, Any?>): String {
        val json = JSONObject(map)
        return json.toString()
    }

    fun logEvent(context: Context, event: String, props: String?) {
        val eventIntent = Intent("com.bukuwarung.action.ACTION_LOG_EVENT")
        eventIntent.putExtra("key", event)
        eventIntent.putExtra("props", props)
        LocalBroadcastManager.getInstance(context).sendBroadcast(eventIntent)
    }

    fun addExtraEventProps(eventProps: String?, props: MutableMap<String?, Any?>) {
        eventProps?.let {
            val propsJson: JsonObject? = Gson().fromJson(it, JsonObject::class.java)
            propsJson?.entrySet()?.forEach {
                props[it.key] = it.value.toString()
            }
        }
    }
}