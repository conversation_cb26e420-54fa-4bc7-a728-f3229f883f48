package com.bukuwarung.lib.webview.network.session

import com.bukuwarung.lib.webview.network.Networking

class SessionRepository private constructor() : SessionRemoteRepository {

    companion object {
        private val instance = SessionRepository()
        private val network = Networking()
        lateinit var sessionRemoteDataSource: SessionRemoteDataSource

        fun getInstance(authSvcUrl: String): SessionRepository {
            network.setBaseUrl(authSvcUrl)
            sessionRemoteDataSource = network.provideSessionRemoteDataSource()
            return instance
        }
    }

    override suspend fun createNewSession(newSessionRequest: NewSessionRequest) =
        sessionRemoteDataSource.createNewSession(newSessionRequest)
}

interface SessionRemoteRepository {
    suspend fun createNewSession(newSessionRequest: NewSessionRequest): NewSessionResponse
}