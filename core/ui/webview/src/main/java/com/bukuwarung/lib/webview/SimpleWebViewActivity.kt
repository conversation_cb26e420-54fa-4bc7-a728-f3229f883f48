package com.bukuwarung.lib.webview

import android.content.Context
import android.content.Intent

class SimpleWebViewActivity: BaseWebviewActivity() {
    companion object {

        fun createIntent(origin: Context?, link: String?, title: String?): Intent? {
            val intent = Intent(origin, SimpleWebViewActivity::class.java)
            intent.putExtra(LINK, link)
            intent.putExtra(TITLE, title)
            return intent
        }

    }
    override fun getLink(): String? {
        return intent?.getStringExtra(LINK)
    }

    override fun getTitleText(): String? {
        return intent?.getStringExtra(TITLE)
    }

    override fun getDeeplinkScheme(): String? {
        return null
    }

    override fun allowDebug(): <PERSON><PERSON><PERSON> {
        return false
    }

    override fun hideToolBar(): <PERSON><PERSON>an {
        return false
    }

    override fun showError(isServiceError: <PERSON><PERSON><PERSON>, message: String?) {
        // do nothing
    }

}
