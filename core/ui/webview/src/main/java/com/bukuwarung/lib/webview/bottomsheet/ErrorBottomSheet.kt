package com.bukuwarung.lib.webview.bottomsheet

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.bukuwarung.lib.webview.R
import com.bukuwarung.lib.webview.databinding.ErrorBottomSheetBinding


class ErrorBottomSheet : BaseBottomSheetDialogFragment() {

    companion object {
        const val TAG = "ErrorBottomSheet"
        const val IS_SERVICE_DOWN = "is_service_down"
        const val MESSAGE = "message"
        const val CALL_ACTION_ON_DISMISS = "call_action_on_dismiss"
        const val SET_CANCELLABLE = "set_cancellable"

        fun createInstance(
            isServiceDown: Boolean,
            errorMessage: String?,
            callActionOnDismiss: Boolean = false,
            setCancellable: Boolean = true
        ) = ErrorBottomSheet().apply {
            val bundle = Bundle()
            bundle.putBoolean(IS_SERVICE_DOWN, isServiceDown)
            bundle.putString(MESSAGE, errorMessage)
            bundle.putBoolean(CALL_ACTION_ON_DISMISS, callActionOnDismiss)
            bundle.putBoolean(SET_CANCELLABLE, setCancellable)
            arguments = bundle
        }
    }

    interface Callback {
        fun action()
    }

    private lateinit var binding: ErrorBottomSheetBinding
    private var callback: Callback? = null
    private val isServiceDown by lazy { arguments?.getBoolean(IS_SERVICE_DOWN) ?: false }
    private val message by lazy { arguments?.getString(MESSAGE) }
    private val callActionOnDismiss by lazy {
        arguments?.getBoolean(CALL_ACTION_ON_DISMISS) ?: false
    }
    private val setCancellable by lazy { arguments?.getBoolean(SET_CANCELLABLE) ?: true }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        isCancelable = setCancellable
        binding = ErrorBottomSheetBinding.inflate(inflater, container, false)
        binding.ivClose.setOnClickListener {
            dismiss()
            if (callActionOnDismiss) callback?.action()
        }
        if (isServiceDown) {
            binding.ivIcPaymentDown.setImageResource(R.drawable.ic_server_busy)
            binding.tvPaymentDownTitle.setText(R.string.lib_disturbance_message)
            binding.tvPaymentDownBody.text =
                if (message.isNullOrBlank()) getString(R.string.lib_try_later) else message
            binding.btnPaymentDown.setText(R.string.lib_back)
            binding.btnPaymentDown.setOnClickListener {
                dismiss()
                if (callActionOnDismiss) callback?.action()
            }
        } else {
            binding.btnPaymentDown.setOnClickListener {
                dismiss()
                callback?.action()
            }
        }
        return binding.root
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        callback = context as? Callback
    }
}
