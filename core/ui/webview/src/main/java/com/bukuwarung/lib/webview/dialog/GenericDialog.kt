package com.bukuwarung.lib.webview.dialog

import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.widget.Button
import android.widget.TextView
import com.bukuwarung.lib.webview.R
import com.bukuwarung.lib.webview.util.hideView

class GenericDialog(
    context: Context,
    private val layoutRes: Int,
    private val titleRes: Int,
    private val bodyRes: Int,
    private val btnRightRes: Int,
    private val btnLeftRes: Int?,
    private val rightBtnCallback: (() -> Unit)? = null,
    private val leftBtnCallback: (() -> Unit)? = null
) : Dialog(context) {

    private constructor(b: Builder) : this(b.context, b.layoutRes, b.titleRes, b.bodyRes, b.btnRightRes, b.btnLeftRes, b.rightBtnCallback, b.leftBtnCallback)

    companion object {
        fun create(context: Context, init: Builder.() -> Unit) = Builder(context, init).build()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        setContentView(layoutRes)
        setCancelable(false)
        super.onCreate(savedInstanceState)
        setupView()
    }

    private fun setupView() {

        val title = findViewById<TextView>(R.id.tv_title)
        val body = findViewById<TextView>(R.id.tv_body)
        val btnRight = findViewById<Button>(R.id.btn_right)
        val btnLeft = findViewById<Button>(R.id.btn_left)

        title.text = context.getString(titleRes)
        body.text = context.getString(bodyRes)
        btnRight.text = context.getString(btnRightRes)
        btnLeftRes?.let {
            btnLeft.text = context.getString(it)
        } ?: run {
            btnLeft.hideView()
        }

        btnRight.setOnClickListener {
            rightBtnCallback?.invoke()
            dismiss()
        }
        btnLeft.setOnClickListener {
            leftBtnCallback?.invoke()
            dismiss()
        }
    }

    class Builder private constructor() {

        constructor(_context: Context, init: Builder.() -> Unit) : this() {
            context { _context }
            layoutRes { R.layout.generic_dialog }
            init()
        }

        lateinit var context: Context
        var layoutRes: Int = 0
        var titleRes: Int = 0
        var bodyRes: Int = 0
        var btnRightRes: Int = 0
        var btnLeftRes: Int? = 0

        var rightBtnCallback: (() -> Unit)? = null
        var leftBtnCallback: (() -> Unit)? = null

        private fun context(init: Builder.() -> Context) = apply { context = init() }
        private fun layoutRes(init: Builder.() -> Int) = apply { layoutRes = init() }
        fun titleRes(init: Builder.() -> Int) = apply { titleRes = init() }
        fun bodyRes(init: Builder.() -> Int) = apply { bodyRes = init() }
        fun btnRightRes(init: Builder.() -> Int) = apply { btnRightRes = init() }
        fun btnLeftRes(init: Builder.() -> Int) = apply { btnLeftRes = init() }

        fun build() = GenericDialog(this)
    }


}