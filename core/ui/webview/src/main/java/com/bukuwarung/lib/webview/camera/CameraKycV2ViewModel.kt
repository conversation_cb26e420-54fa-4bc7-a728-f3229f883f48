package com.bukuwarung.lib.webview.camera

import android.app.Application
import androidx.lifecycle.*
import com.bukuwarung.lib.webview.data.ImageUploads
import com.bukuwarung.lib.webview.data.LivelinessDetails
import com.bukuwarung.lib.webview.network.KYCProvider
import com.bukuwarung.lib.webview.network.KycRepository
import com.bukuwarung.lib.webview.network.SessionUseCase
import com.bukuwarung.lib.webview.util.Constant.MIME_TYPE_JPEG
import com.bukuwarung.lib.webview.util.Utils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.RequestBody
import okhttp3.RequestBody.Companion.asRequestBody
import retrofit2.HttpException
import java.io.File
import java.io.InterruptedIOException
import java.net.ConnectException
import java.net.UnknownHostException


class CameraKycV2ViewModel(application: Application) : AndroidViewModel(application),
    DefaultLifecycleObserver {

    private val _event = MutableLiveData<Event>()
    val event: LiveData<Event> = _event
    private lateinit var appBaseUrl: String
    private lateinit var accountVerificationUrl: String
    private var refreshTokenRetry = 0
    private var activityStopped = false

    override fun onStop(owner: LifecycleOwner) {
        super.onStop(owner)
        activityStopped = true
    }

    override fun onResume(owner: LifecycleOwner) {
        super.onResume(owner)
        activityStopped = false
    }

    sealed class Event {
        data class ApiError(val isServerError: Boolean, val errorMessage: String) : Event()
        object NetworkError : Event()
        data class Loading(val isLoading: Boolean) : Event()
        data class KycProviderReceived(val kycProvider: KYCProvider) : Event()
        data class SelfieUploadSuccess(val livenessResult: Boolean?) : Event()
        object KycProviderMismatch : Event()
    }

    fun uploadImage(
        photoFile: File, authToken: String?,
        authUrl: String, sessionToken: String,
        kycProvider: KYCProvider,
        kycFlow: String?,
        reKycType: String?,
        isMiniAtm: Boolean = false
    ) {
        showLoader(true)
        val repository = KycRepository.getInstance(appBaseUrl)
        val auth = "Bearer $authToken"

        val reqBody: RequestBody = photoFile.asRequestBody(MIME_TYPE_JPEG.toMediaTypeOrNull())
        val filePart = MultipartBody.Part.createFormData("file", photoFile.name, reqBody)

        val bukuOrigin = if (isMiniAtm) "ext-miniatm-partner" else "bukuwarung-app"
        viewModelScope.launch(Dispatchers.IO) {
            try {
                repository.uploadKycSelfieMultipartWithAuth(auth, kycProvider, kycFlow, reKycType, filePart, bukuOrigin)
                showLoader(false)
                viewModelScope.launch(Dispatchers.Main) {
                    _event.value = Event.SelfieUploadSuccess(null)
                }
            } catch (ex: Exception) {
                /*
                    Added refreshTokenRetry to break recursion just in case auth apis sends
                    invalid tokens.
                 */
                if ((ex as? HttpException)?.code() == 401 && refreshTokenRetry < 2) {
                    try {
                        refreshTokenRetry++
                        val newSession =
                                SessionUseCase.createNewSession(authUrl, sessionToken)
                        uploadImage(
                            photoFile,
                            newSession.idToken,
                            authUrl,
                            newSession.refreshToken,
                            kycProvider,
                            kycFlow,
                            reKycType,
                            isMiniAtm
                        )
                    } catch (ex: Exception) {
                        if ((ex as? HttpException)?.code() == 422) {
                            showLoader(false)
                            viewModelScope.launch(Dispatchers.Main) {
                                _event.value = Event.KycProviderMismatch
                            }
                        } else {
                            handleException(ex)
                        }
                    }
                } else if ((ex as? HttpException)?.code() == 422) {
                    // Currently expecting 422 to be due to inconsistency in kyc provider only
                    // In future, we need to add call adapter factory to handle different
                    // types of error in the same error code.
                    showLoader(false)
                    if (activityStopped) {
                        Utils.openUrl(
                            getApplication<Application>().applicationContext,
                            accountVerificationUrl
                        )
                    } else {
                        viewModelScope.launch(Dispatchers.Main) {
                            _event.value = Event.KycProviderMismatch
                        }
                    }
                } else {
                    handleException(ex)
                }
            }
        }
    }

    private fun handleException(ex: Exception) {
        Utils.logException(getApplication<Application>().applicationContext, ex)
        viewModelScope.launch(Dispatchers.Main) {
            if (activityStopped) {
                Utils.openUrl(
                    getApplication<Application>().applicationContext,
                    accountVerificationUrl
                )
            } else {
                _event.value = when (ex) {
                    is UnknownHostException, is ConnectException, is InterruptedIOException -> Event.NetworkError
                    else -> Event.ApiError(true, ex.localizedMessage.orEmpty())
                }
                showLoader(false)
            }
        }
    }

    fun showLoader(isLoading: Boolean) {
        viewModelScope.launch(Dispatchers.Main) {
            _event.value = Event.Loading(isLoading)
        }
    }

    fun setBaseUrl(baseUrl: String?) {
        baseUrl?.let {
            this.appBaseUrl = baseUrl
        }
    }

    fun setAccountVerificationUrl(accountVerificationUrl: String?) {
        accountVerificationUrl?.let {
            this.accountVerificationUrl = it
        }
    }

    private fun uploadSelfies(
        imageUploads: ImageUploads, authToken: String?, authUrl: String, sessionToken: String,
        livenessResult: Boolean?
    ) {
        showLoader(true)
        val repository = KycRepository.getInstance(appBaseUrl)
        val auth = "Bearer $authToken"

        viewModelScope.launch(Dispatchers.IO) {
            try {
                repository.uploadKycImagesWithAuth(
                    auth,
                    imageUploads
                )
                showLoader(false)
                viewModelScope.launch(Dispatchers.Main) {
                    if (activityStopped) {
                        Utils.openUrl(
                            getApplication<Application>().applicationContext,
                            accountVerificationUrl
                        )
                    } else {
                        _event.value = Event.SelfieUploadSuccess(livenessResult)
                    }
                }
            } catch (ex: Exception) {
                /*
                    Added refreshTokenRetry to break recursion just in case auth apis sends
                    invalid tokens.
                 */
                if ((ex as? HttpException)?.code() == 401 && refreshTokenRetry < 2) {
                    try {
                        refreshTokenRetry++
                        val newSession =
                            SessionUseCase.createNewSession(authUrl, sessionToken)
                        uploadSelfies(
                            imageUploads, newSession.idToken, authUrl,
                            newSession.refreshToken, livenessResult
                        )
                    } catch (ex: Exception) {
                        if ((ex as? HttpException)?.code() == 422) {
                            showLoader(false)
                            viewModelScope.launch(Dispatchers.Main) {
                                if (activityStopped) {
                                    Utils.openUrl(
                                        getApplication<Application>().applicationContext,
                                        accountVerificationUrl
                                    )
                                } else {
                                    _event.value = Event.KycProviderMismatch
                                }
                            }
                        } else {
                            handleException(ex)
                        }
                    }
                } else if ((ex as? HttpException)?.code() == 422) {
                    // Currently expecting 422 to be due to inconsistency in kyc provider only
                    // In future, we need to add call adapter factory to handle different
                    // types of error in the same error code.
                    showLoader(false)
                    viewModelScope.launch(Dispatchers.Main) {
                        if (activityStopped) {
                            Utils.openUrl(
                                    getApplication<Application>().applicationContext,
                                    accountVerificationUrl
                            )
                        } else {
                            _event.value = Event.KycProviderMismatch
                        }
                    }
                } else {
                    handleException(ex)
                }
            }
        }
    }

    fun uploadPrivyImages(
        image1: String, image2: String,
        authToken: String?, authUrl: String, sessionToken: String,
        livenessResult: Boolean, fcToken: String, kycProvider: KYCProvider
    ) {
        val imageUploads = ImageUploads(
            image1, image2, LivelinessDetails(livenessResult, fcToken), kycProvider
        )
        uploadSelfies(imageUploads, authToken, authUrl, sessionToken, livenessResult)
    }

    fun getKycProvider(
        authToken: String?, authUrl: String, sessionToken: String, isMiniAtm: Boolean = false
    ) {
        showLoader(true)

        val repository = KycRepository.getInstance(appBaseUrl)
        val auth = "Bearer $authToken"

        val bukuOrigin = if (isMiniAtm) "ext-miniatm-partner" else "bukuwarung-app"


        viewModelScope.launch(Dispatchers.IO) {
            try {
                val activeProviders = repository.getActiveProviders(auth, bukuOrigin)
                activeProviders.kycProvider?.let { kycProvider ->
                    viewModelScope.launch(Dispatchers.Main) {
                        _event.value = Event.KycProviderReceived(kycProvider)
                    }
                }
                showLoader(false)
            } catch (ex: Exception) {
                /*
                Added refreshTokenRetry to break recursion just in case auth apis sends
                invalid tokens.
             */
                if ((ex as? HttpException)?.code() == 401 && refreshTokenRetry < 2) {
                    try {
                        refreshTokenRetry++
                        val newSession =
                            SessionUseCase.createNewSession(authUrl, sessionToken)
                        getKycProvider(
                            newSession.idToken, authUrl, newSession.refreshToken
                        )
                    } catch (ex: Exception) {
                        handleException(ex)
                    }
                } else {
                    handleException(ex)
                }
            }
        }
    }
}