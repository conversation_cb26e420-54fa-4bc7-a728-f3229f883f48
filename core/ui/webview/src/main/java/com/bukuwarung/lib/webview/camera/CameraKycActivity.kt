package com.bukuwarung.lib.webview.camera

import android.Manifest
import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.IntentSender
import android.content.pm.PackageManager
import android.location.LocationManager
import android.net.Uri
import android.os.Bundle
import android.provider.Settings
import android.util.Log
import android.view.View
import android.widget.ImageView
import android.widget.ProgressBar
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.camera.core.*
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.view.PreviewView
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.core.location.LocationManagerCompat
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.bukuwarung.lib.webview.R
import com.bukuwarung.lib.webview.SimpleWebViewActivity
import com.bukuwarung.lib.webview.camera.overlay.OverlayView
import com.bukuwarung.lib.webview.databinding.ActivityCameraBinding
import com.bukuwarung.lib.webview.dialog.GenericDialog
import com.bukuwarung.lib.webview.geocoding.FetchingLocationDialog
import com.bukuwarung.lib.webview.geocoding.GeocodingViewModel
import com.bukuwarung.lib.webview.geocoding.LocationNotDetectedBottomSheet
import com.bukuwarung.lib.webview.util.AnalyticsUtil
import com.bukuwarung.lib.webview.util.Constant
import com.bukuwarung.lib.webview.util.Constant.ADDRESS
import com.bukuwarung.lib.webview.util.Constant.HELP_LINK
import com.bukuwarung.lib.webview.util.Constant.TYPE_BANK
import com.bukuwarung.lib.webview.util.Constant.TYPE_BPJS
import com.bukuwarung.lib.webview.util.Constant.TYPE_FAMILY_PHOTO
import com.bukuwarung.lib.webview.util.Constant.TYPE_IN_STORE
import com.bukuwarung.lib.webview.util.Constant.TYPE_KYB_INVENTORY
import com.bukuwarung.lib.webview.util.Constant.TYPE_KYB_STORE
import com.bukuwarung.lib.webview.util.Constant.TYPE_MH_TICKET
import com.bukuwarung.lib.webview.util.Constant.TYPE_NPWP
import com.bukuwarung.lib.webview.util.Constant.TYPE_PHYSICAL_VISIT_STORE
import com.bukuwarung.lib.webview.util.Constant.TYPE_STORE_FRONT
import com.bukuwarung.lib.webview.util.hideView
import com.bukuwarung.lib.webview.util.isTrue
import com.bumptech.glide.Glide
import com.bumptech.glide.request.RequestOptions
import com.google.android.gms.common.api.ResolvableApiException
import com.google.android.gms.location.*
import com.google.android.gms.tasks.CancellationTokenSource
import kotlinx.coroutines.launch
import java.io.File
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors


class CameraKycActivity : AppCompatActivity(), LocationNotDetectedBottomSheet.ICommunicator {
    private var imageCapture: ImageCapture? = null

    private lateinit var outputDirectory: File
    private lateinit var cameraExecutor: ExecutorService
    private lateinit var preview: PreviewView
    private var lensFacing = CameraSelector.LENS_FACING_BACK
    private lateinit var cameraProvider: ProcessCameraProvider
    private var camera: Camera? = null
    private var flashType = ImageCapture.FLASH_MODE_OFF
    private var overlayView: OverlayView? = null
    private var imageAnalysis: ImageAnalysis? = null
    private val type by lazy {
        intent?.getStringExtra(TYPE) ?: ""
    }
    private var address: String? = null
    private lateinit var binding: ActivityCameraBinding
    private val titleMessage by lazy { intent?.getStringExtra(TITLE) }
    private val hintMessage by lazy { intent?.getStringExtra(HINT_MESSAGE) }
    private val useCase by lazy { intent?.getSerializableExtra(USE_CASE) as? UseCase ?: UseCase.IMAGE }
    private val checkLuminosity by lazy { intent?.getBooleanExtra(CHECK_LUMINOSITY, false) }
    private val minLuminosity by lazy { intent?.getFloatExtra(MIN_LUMINOSITY, 0f) }
    private val isMiniAtm by lazy { intent?.getBooleanExtra(IS_MINIATM, false).isTrue }
    private lateinit var luminosityAnalyzer: LuminosityAnalyzer
    private lateinit var viewModel: GeocodingViewModel
    private var dialog: FetchingLocationDialog? = null
    private var locationRequestStartedTimeInMillis = 0L
    private var locationFoundTimeInMillis = 0L
    private var hasOpenedSettingsForLocationPermission: Boolean = false
    private var hasOpenedSettingsForCameraPermission: Boolean = false
    private lateinit var fusedLocationProviderClient: FusedLocationProviderClient
    private val cancellationTokenSource = CancellationTokenSource()
    private val locationPermission = Manifest.permission.ACCESS_FINE_LOCATION
    private val cameraPermission = Manifest.permission.CAMERA
    private val locationRequest: LocationRequest = LocationRequest.create().apply {
        interval = DEFAULT_INTERVAL
        fastestInterval = DEFAULT_FASTEST_INTERVAL
        priority = DEFAULT_PRIORITY
        maxWaitTime = DEFAULT_MAX_WAIT_TIME
    }

    companion object {
        const val FILE_PATH = "filePath"
        const val IMAGE_WIDTH = "imageWidth"
        const val IMAGE_HEIGHT = "imageHeight"
        const val IMAGE_X_AXIS = "imageXAxis"
        const val IMAGE_Y_AXIS = "imageYAxis"
        private const val TAG = "CameraXBasic"
        const val TYPE = "type"
        const val TITLE = "title"
        const val HINT_MESSAGE = "hint_message"
        const val USE_CASE = "useCase"
        private const val CAMERA_REQUEST_CODE = 414
        private const val CHECK_LUMINOSITY = "check_luminosity"
        private const val MIN_LUMINOSITY = "min_luminosity"
        // Location values
        private const val DEFAULT_INTERVAL: Long = 30
        private const val DEFAULT_FASTEST_INTERVAL: Long = 10
        private const val DEFAULT_MAX_WAIT_TIME: Long = 60
        private const val DEFAULT_PRIORITY = Priority.PRIORITY_HIGH_ACCURACY
        private const val LOCATION_REQUEST_CODE = 212
        private const val GPS_REQUEST_CODE = 313
        private const val IS_MINIATM = "is_miniatm"

        fun createIntent(
            origin: Context?,
            type: String?,
            title: String? = null,
            hintMessage: String? = null,
            useCase: UseCase? = UseCase.IMAGE,
            checkLuminosity: Boolean = false,
            minLuminosity: Float = 0f,
            isMiniAtm: Boolean = false
        ): Intent {
            val intent = Intent(origin, CameraKycActivity::class.java)
            intent.putExtra(TYPE, type)
            intent.putExtra(TITLE, title)
            intent.putExtra(HINT_MESSAGE, hintMessage)
            intent.putExtra(USE_CASE, useCase)
            intent.putExtra(CHECK_LUMINOSITY, checkLuminosity)
            intent.putExtra(MIN_LUMINOSITY, minLuminosity)
            intent.putExtra(IS_MINIATM, isMiniAtm)
            return intent
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding = ActivityCameraBinding.inflate(layoutInflater)
        setContentView(binding.root)
        viewModel = ViewModelProvider(this).get(GeocodingViewModel::class.java)

        val captureBtn = findViewById<ImageView>(R.id.camera_capture_btn)
        val flashBtn = findViewById<ImageView>(R.id.flash_btn)
        preview = findViewById(R.id.preview_camera)
        overlayView = findViewById<OverlayView>(R.id.overlay_view)
        val switchBtn = findViewById<ImageView>(R.id.switch_btn)
        if (type == TYPE_MH_TICKET || Constant.lendingConstants.contains(type)) {
            overlayView?.visibility = View.GONE
        } else {
            if (type == Constant.TYPE_SELFIE_KTP) {
                // If type is selfie we hide option to switch camera
                switchBtn.visibility = View.GONE
            }
            overlayView?.visibility = View.VISIBLE
            overlayView?.setCameraType(type)
        }
        val backBtn = findViewById<ImageView>(R.id.backBtn)
        val helpTxt = findViewById<TextView>(R.id.help_txt)
        if (isMiniAtm) helpTxt.hideView()
        backBtn?.setOnClickListener {
            onBackPressed()
        }
        helpTxt?.setOnClickListener {
            startActivity(SimpleWebViewActivity.createIntent(this, HELP_LINK, getString(R.string.lib_help_bantuan)))
        }
        handleType()

        if (useCase == UseCase.IMAGE) {
            getCameraPermission()
        } else if (useCase == UseCase.IMAGE_AND_LOCATION) {
            getLocationPermission()
        }

        captureBtn.setOnClickListener {
            findViewById<ProgressBar>(R.id.progress_bar).visibility = View.VISIBLE
            captureBtn.visibility = View.INVISIBLE
            takePhoto()
        }

        outputDirectory = getOutputDirectory()

        cameraExecutor = Executors.newSingleThreadExecutor()
        // Listener for button used to switch cameras

        luminosityAnalyzer = LuminosityAnalyzer { luma ->
            runOnUiThread {
                if (luma < (minLuminosity ?: 0f)) {
                    binding.tvError.visibility = View.VISIBLE
                    captureBtn.isEnabled = false
                } else {
                    binding.tvError.visibility = View.GONE
                    captureBtn.isEnabled = true
                }
            }
        }

        switchBtn.setOnClickListener {
            lensFacing = if (CameraSelector.LENS_FACING_BACK == lensFacing) {
                CameraSelector.LENS_FACING_FRONT
            } else {
                CameraSelector.LENS_FACING_BACK
            }
            startCamera()
        }
        flashBtn.setOnClickListener {
            if (camera?.cameraInfo?.hasFlashUnit() != true) {
                return@setOnClickListener
            }
            flashType = when (flashType) {
                ImageCapture.FLASH_MODE_OFF -> ImageCapture.FLASH_MODE_ON
                ImageCapture.FLASH_MODE_ON -> ImageCapture.FLASH_MODE_AUTO
                else -> ImageCapture.FLASH_MODE_OFF
            }
            imageCapture?.flashMode = flashType
            flashBtn.setImageResource(when (flashType) {
                ImageCapture.FLASH_MODE_ON -> R.drawable.ic_flash_on
                ImageCapture.FLASH_MODE_AUTO -> R.drawable.ic_flash_auto
                else -> R.drawable.ic_flash_off
            })
        }
        subscribeState()
    }

    private fun getLocationPermission() {
        if (ContextCompat.checkSelfPermission(this, locationPermission) == PackageManager.PERMISSION_GRANTED) {
            startLocationCallback()
        } else {
            ActivityCompat.requestPermissions(this, arrayOf(locationPermission), LOCATION_REQUEST_CODE)
        }
    }

    private fun getCameraPermission() {
        if (ContextCompat.checkSelfPermission(this, cameraPermission) == PackageManager.PERMISSION_GRANTED) {
            startCamera()
        } else {
            ActivityCompat.requestPermissions(this, arrayOf(cameraPermission), CAMERA_REQUEST_CODE)
        }
    }

    private fun handleType() {
        val guidelineTxt = findViewById<TextView>(R.id.guideline_txt)
        val title = findViewById<TextView>(R.id.title)
        when (type) {
            Constant.TYPE_KTP -> {
                lensFacing = CameraSelector.LENS_FACING_BACK
                guidelineTxt.text = getString(R.string.ktp_guide)
                title.text = getString(R.string.take_id_card)
            }
            TYPE_FAMILY_PHOTO -> {
                lensFacing = CameraSelector.LENS_FACING_BACK
                guidelineTxt.text = getString(R.string.guideline_text_family_photo)
                title.text = getString(R.string.title_family_photo)
            }
            TYPE_BANK -> {
                lensFacing = CameraSelector.LENS_FACING_BACK
                guidelineTxt.text = getString(R.string.guideline_text_family_photo)
                title.text = getString(R.string.title_Bank)
            }
            TYPE_BPJS -> {
                lensFacing = CameraSelector.LENS_FACING_BACK
                guidelineTxt.text = getString(R.string.guideline_text_BPJS)
                title.text = getString(R.string.title_BPJS)
            }
            TYPE_NPWP -> {
                lensFacing = CameraSelector.LENS_FACING_BACK
                guidelineTxt.text = getString(R.string.guideline_text_family_photo)
                title.text = getString(R.string.title_NPWP)
            }
            TYPE_STORE_FRONT, TYPE_IN_STORE -> {
                lensFacing = CameraSelector.LENS_FACING_BACK
                guidelineTxt.text = hintMessage
                title.text = titleMessage
            }
            TYPE_KYB_STORE, TYPE_PHYSICAL_VISIT_STORE -> {
                lensFacing = CameraSelector.LENS_FACING_BACK
                title.text = getString(R.string.lib_title_kyb_store)
                guidelineTxt.text = getString(R.string.lib_guideline_text_kyb_store)
                address = intent.getStringExtra(ADDRESS)
            }
            TYPE_KYB_INVENTORY -> {
                lensFacing = CameraSelector.LENS_FACING_BACK
                title.text = getString(R.string.lib_title_kyb_inventory)
                guidelineTxt.text = getString(R.string.lib_guideline_text_kyb_inventory)
            }
            else -> {
                lensFacing = CameraSelector.LENS_FACING_FRONT
                if (type == Constant.TYPE_SELFIE_KTP) {
                    guidelineTxt.visibility = View.GONE
                }
                guidelineTxt.text = getString(R.string.selfie_guide)
                title.text = getString(R.string.take_selfie)
            }
        }
    }

    private fun takePhoto() {
        // Get a stable reference of the modifiable image capture use case
        val imageCapture = imageCapture ?: return

        // Create time-stamped output file to hold the image
        val photoFile = when (type) {
            TYPE_MH_TICKET -> File(outputDirectory, "$type.jpg")
            TYPE_STORE_FRONT, TYPE_IN_STORE -> File(
                outputDirectory,
                "$type${System.currentTimeMillis()}.jpg"
            )
            else -> File(outputDirectory, "kyc$type.jpg")
        }

        // Create output options object which contains file + metadata
        val outputOptions = ImageCapture.OutputFileOptions.Builder(photoFile).build()

        // Set up image capture listener, which is triggered after photo has
        // been taken
        imageCapture.takePicture(
            outputOptions,
            ContextCompat.getMainExecutor(this),
            object : ImageCapture.OnImageSavedCallback {
                override fun onError(exc: ImageCaptureException) {
                    Log.e(TAG, "Photo capture failed: ${exc.message}", exc)
                    Toast.makeText(
                        this@CameraKycActivity,
                        "Error: ${exc.message}",
                        Toast.LENGTH_SHORT
                    ).show()
                }

                override fun onImageSaved(output: ImageCapture.OutputFileResults) {
                    if (type == TYPE_IN_STORE || type == TYPE_STORE_FRONT) {
                        cameraProvider.unbindAll()
                        // For Lending types, we show preview in this activity itself
                        with(binding) {
                            Glide.with(ivImagePreview)
                                .load(output.savedUri)
                                .apply(RequestOptions.fitCenterTransform())
                                .into(ivImagePreview)
                            btnRetake.setOnClickListener {
                                grImageCapturing.visibility = View.VISIBLE
                                clImagePreview.visibility = View.GONE
                                startCamera()
                            }
                            btnContinue.setOnClickListener { setResults(photoFile) }
                            grImageCapturing.visibility = View.GONE
                            progressBar.visibility = View.GONE
                            clImagePreview.visibility = View.VISIBLE
                        }
                    } else {
                        setResults(photoFile)
                    }
                }
            })
    }

    private fun setResults(photoFile: File) {
        setResult(
            Activity.RESULT_OK, Intent()
                .putExtra(FILE_PATH, photoFile.absolutePath)
                .putExtra(TYPE, type)
                .putExtra(IMAGE_HEIGHT, overlayView?.heightImage)
                .putExtra(IMAGE_WIDTH, overlayView?.widthImage)
                .putExtra(IMAGE_X_AXIS, overlayView?.leftImage)
                .putExtra(IMAGE_Y_AXIS, overlayView?.topImage)
                .putExtra(ADDRESS, address)
        )
        finish()
    }

    private fun startCamera() {
        val cameraProviderFuture = ProcessCameraProvider.getInstance(this)

        cameraProviderFuture.addListener(Runnable {
            // Used to bind the lifecycle of cameras to the lifecycle owner
            cameraProvider = cameraProviderFuture.get()
            val cameraSelector = CameraSelector.Builder().requireLensFacing(lensFacing).build()

            // Preview
            val preview = Preview.Builder()
                    .build()
                    .also {
                        it.setSurfaceProvider(preview.surfaceProvider)
                    }

            imageCapture = ImageCapture.Builder().setFlashMode(flashType).build()

            // Select back camera as a default

            try {
                // Unbind use cases before rebinding
                cameraProvider.unbindAll()
                // Bind use cases to camera
                imageAnalysis = ImageAnalysis.Builder().build()
                if (checkLuminosity == true) {
                    imageAnalysis?.setAnalyzer(cameraExecutor, luminosityAnalyzer)
                }
                camera = cameraProvider.bindToLifecycle(this, cameraSelector, preview, imageCapture, imageAnalysis)

            } catch (exc: Exception) {
                Log.e(TAG, "Use case binding failed", exc)
            }

        }, ContextCompat.getMainExecutor(this))
    }

    private fun getOutputDirectory(): File {
        val mediaDir = externalMediaDirs.firstOrNull()?.let {
            File(
                it, if (type == TYPE_MH_TICKET) {
                    resources.getString(R.string.file_name_mh)
                } else {
                    resources.getString(R.string.file_name)
                }
            ).apply { mkdirs() }
        }
        return if (mediaDir != null && mediaDir.exists())
            mediaDir else filesDir
    }

    override fun onDestroy() {
        super.onDestroy()
        cameraExecutor.shutdown()
    }

    override fun onBackPressed() {
        GenericDialog.create(context = this){
            titleRes = R.string.lib_exit_confirmation_title
            bodyRes = R.string.lib_exit_confirmation_body
            btnLeftRes = R.string.lib_batal
            btnRightRes = R.string.lib_exit_confirm
            rightBtnCallback = {
                super.onBackPressed()
            }
        }.show()
    }

    private fun handleCameraPermissionDenial() {
        GenericDialog.create(context = this){
            titleRes = R.string.aw_permission_required
            bodyRes = R.string.aw_camera_permission_denied
            btnLeftRes = R.string.lib_batal
            btnRightRes = R.string.aw_give_permission
            leftBtnCallback = {
                finish()
            }
            rightBtnCallback = {
                reAskCameraPermission()
            }
        }.show()
    }

    private fun reAskCameraPermission() {
        if (ActivityCompat.shouldShowRequestPermissionRationale(this, Manifest.permission.CAMERA)) {
            ActivityCompat.requestPermissions(this, arrayOf(cameraPermission), CAMERA_REQUEST_CODE)
        } else {
            hasOpenedSettingsForCameraPermission = true
            startActivity(
                Intent(
                    Settings.ACTION_APPLICATION_DETAILS_SETTINGS,
                    Uri.fromParts("package", this.packageName, null),
                ),
            )
        }
    }


    // Methods related to the IMAGE_AND_LOCATION use case

    override fun onResume() {
        super.onResume()
        if (hasOpenedSettingsForLocationPermission) {
            hasOpenedSettingsForLocationPermission = false
            if (ContextCompat.checkSelfPermission(
                    this,
                    locationPermission
                ) == PackageManager.PERMISSION_GRANTED
            ) {
                if (isLocationEnabled()) {
                    getCurrentLocation()
                } else {
                    startLocationCallback()
                }
            } else {
                showLocationNotDetectedBottomSheet()
            }
        } else if (hasOpenedSettingsForCameraPermission) {
            hasOpenedSettingsForCameraPermission = false
            if (ContextCompat.checkSelfPermission(
                    this,
                    cameraPermission
                ) == PackageManager.PERMISSION_GRANTED
            ) {
                startCamera()
            } else {
                handleCameraPermissionDenial()
            }
        }
    }

    private fun subscribeState() {
        viewModel.observeEvent.observe(this) {
            when(it) {
                is GeocodingViewModel.Event.AddressFound -> {
                    address = it.address
                    locationFoundTimeInMillis = System.currentTimeMillis()
                    trackLocationEvent()
                    showFetchingLocationDialog(false)
                    getCameraPermission()
                }

                is GeocodingViewModel.Event.AddressError -> {
                    finish()
                }
            }
        }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<String>,
        grantResults: IntArray
    ) {
        when (requestCode) {
            LOCATION_REQUEST_CODE -> {
                if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    startLocationCallback()
                } else {
                    showLocationNotDetectedBottomSheet()
                }
            }
            CAMERA_REQUEST_CODE -> {
                if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    startCamera()
                } else {
                    handleCameraPermissionDenial()
                }
            }
            else -> super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (GPS_REQUEST_CODE == requestCode) {
            if (RESULT_OK == resultCode) {
                getCurrentLocation()
            } else {
                showLocationNotDetectedBottomSheet()
            }
        }
    }

    override fun handleBottomSheetClick(useCase: LocationNotDetectedBottomSheet.UseCase) {
        if (ContextCompat.checkSelfPermission(this, locationPermission) != PackageManager.PERMISSION_GRANTED) {
            if (!ActivityCompat.shouldShowRequestPermissionRationale(this,
                    Manifest.permission.ACCESS_FINE_LOCATION)) {
                hasOpenedSettingsForLocationPermission = true
                startActivity(
                    Intent(
                        Settings.ACTION_APPLICATION_DETAILS_SETTINGS,
                        Uri.fromParts("package", this.packageName, null),
                    ),
                )
            } else {
                ActivityCompat.requestPermissions(this, arrayOf(locationPermission), LOCATION_REQUEST_CODE)
            }
        } else if (!isLocationEnabled()) {
            startLocationCallback()
        }
    }

    private fun isLocationEnabled(): Boolean {
        val manager: LocationManager = this.getSystemService(LOCATION_SERVICE) as LocationManager
        return LocationManagerCompat.isLocationEnabled(manager)
    }

    private fun startLocationCallback() {
        val builder = LocationSettingsRequest.Builder()
            .addLocationRequest(locationRequest)
        LocationServices
            .getSettingsClient(this)
            .checkLocationSettings(builder.build())
            .addOnSuccessListener(this) {
                getCurrentLocation()
            }
            .addOnFailureListener(this) { ex: Exception? ->
                if (ex is ResolvableApiException) {
                    try {
                        ex.startResolutionForResult(this@CameraKycActivity, GPS_REQUEST_CODE)
                    } catch (sendEx: IntentSender.SendIntentException) {
                        sendEx.printStackTrace()
                    }
                }
            }
    }

    @SuppressLint("MissingPermission")
    private fun getCurrentLocation() {
        locationRequestStartedTimeInMillis = System.currentTimeMillis()
        fusedLocationProviderClient = LocationServices.getFusedLocationProviderClient(this)
        showFetchingLocationDialog(true)
        lifecycleScope.launch {
            fusedLocationProviderClient.getCurrentLocation(
                Priority.PRIORITY_HIGH_ACCURACY,
                cancellationTokenSource.token
            ).addOnSuccessListener {
                it?.let {
                    viewModel.geocodeAddress(it.latitude, it.longitude)
                }
            }
        }
    }

    private fun showFetchingLocationDialog(show: Boolean) {
        if (show) {
            dialog = FetchingLocationDialog(this)
            dialog?.show()
        } else {
            dialog?.dismiss()
        }
    }

    private fun showLocationNotDetectedBottomSheet() {
        LocationNotDetectedBottomSheet.createIntent(isCancellable = false).show(supportFragmentManager, LocationNotDetectedBottomSheet.TAG)
    }

    private fun trackLocationEvent() {
        val props = mutableMapOf<String?, Any?>()
        props[AnalyticsUtil.GET_LOCATION_PERMISSION] = AnalyticsUtil.YES
        props[AnalyticsUtil.GET_LOCATION] = AnalyticsUtil.YES
        props[AnalyticsUtil.GET_LOCATION_TIME_IN_SECONDS] = (locationFoundTimeInMillis - locationRequestStartedTimeInMillis)/1000
        props[AnalyticsUtil.ENTRY_POINT] = if (type == TYPE_KYB_STORE) AnalyticsUtil.KYB else AnalyticsUtil.PHYSICAL_VISIT
        AnalyticsUtil.logEvent(
            this,
            AnalyticsUtil.EVENT_KYB_TAKE_STORE_PHOTO_CLICKED,
            AnalyticsUtil.getJsonStringOfMap(props)
        )
    }

    enum class UseCase {
        IMAGE, IMAGE_AND_LOCATION
    }
}
