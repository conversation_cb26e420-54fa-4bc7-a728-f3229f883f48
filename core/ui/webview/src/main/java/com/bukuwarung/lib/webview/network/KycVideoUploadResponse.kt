package com.bukuwarung.lib.webview.network

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName
@Keep
data class KycVideoUploadResponse(
    @SerializedName("type") var type: String? = null,
    @SerializedName("metadata") var metadata: Metadata? = Metadata(),
    @SerializedName("url") var url: String? = null,
    @SerializedName("attachment_id") var attachmentId: String? = null,
    @SerializedName("url_expired_at") var urlExpiredAt: String? = null,
    @SerializedName("created_at") var createdAt: String? = null
)

@Keep
data class Metadata(

    @SerializedName("tnc") var tnc: Boolean? = null,
    @SerializedName("bank_name") var bankName: String? = null,
    @SerializedName("account_number") var accountNumber: String? = null,
    @SerializedName("account_holder_name") var accountHolderName: String? = null

)