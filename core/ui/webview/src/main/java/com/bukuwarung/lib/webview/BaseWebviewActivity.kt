package com.bukuwarung.lib.webview

import android.Manifest
import android.annotation.SuppressLint
import android.app.Activity
import android.app.DownloadManager
import android.content.*
import android.content.pm.ActivityInfo
import android.content.pm.PackageManager
import android.database.Cursor
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Environment
import android.provider.MediaStore
import android.provider.Settings
import android.util.Base64
import android.view.View
import android.view.inputmethod.InputMethodManager
import android.webkit.ValueCallback
import android.webkit.WebChromeClient
import android.webkit.WebView
import android.widget.ImageView
import android.widget.ProgressBar
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.Toolbar
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.lifecycle.ViewModelProvider
import com.bukuwarung.lib.webview.bottomsheet.ErrorBottomSheet
import com.bukuwarung.lib.webview.bottomsheet.SizeLimitErrorBS
import com.bukuwarung.lib.webview.camera.CameraKycActivity
import com.bukuwarung.lib.webview.camera.CameraKycActivity.Companion.IMAGE_HEIGHT
import com.bukuwarung.lib.webview.camera.CameraKycActivity.Companion.IMAGE_WIDTH
import com.bukuwarung.lib.webview.camera.CameraKycActivity.Companion.IMAGE_X_AXIS
import com.bukuwarung.lib.webview.camera.CameraKycActivity.Companion.IMAGE_Y_AXIS
import com.bukuwarung.lib.webview.camera.CameraKycV2Activity
import com.bukuwarung.lib.webview.camera.CameraKycV2ViewModel
import com.bukuwarung.lib.webview.camera.VideoKycActivity
import com.bukuwarung.lib.webview.data.PrivyCredentials
import com.bukuwarung.lib.webview.dialog.GenericDialog
import com.bukuwarung.lib.webview.geocoding.FetchingLocationDialog
import com.bukuwarung.lib.webview.geocoding.GeocodingViewModel
import com.bukuwarung.lib.webview.geocoding.LocationNotDetectedBottomSheet
import com.bukuwarung.lib.webview.jsinterface.BwWebInterface
import com.bukuwarung.lib.webview.network.KYCProvider
import com.bukuwarung.lib.webview.util.*
import com.bukuwarung.lib.webview.util.Constant.TYPE_BMU_RE_KYC
import com.bukuwarung.lib.webview.util.Constant.lendingConstants
import com.bukuwarung.lib.webview.util.Constant.newKycFlowConstants
import com.google.android.gms.location.LocationRequest
import com.google.android.gms.tasks.CancellationTokenSource
import com.google.android.material.snackbar.Snackbar
import com.google.gson.Gson
import com.google.gson.JsonObject
import id.privy.privypass_liveness.builder.PrivypassLivenessBuilder
import id.privy.privypass_merchant_core.constant.PrivypassEnv
import org.json.JSONObject
import java.io.ByteArrayOutputStream
import java.io.File
import java.io.FileInputStream
import java.io.InputStream
import java.lang.ref.WeakReference
import kotlin.math.roundToInt


abstract class BaseWebviewActivity : AppCompatActivity(),
    BwWebInterface.BwWebListener, DefaultWebViewClient.WebViewClientListener,
    ErrorBottomSheet.Callback, LocationNotDetectedBottomSheet.ICommunicator {
    var webView: WebView? = null
    private var backImage: ImageView? = null
    private var titleTv: TextView? = null
    private var toolbar: Toolbar? = null
    private var pbLoading: ProgressBar? = null
    private val kycFlowConstants = arrayListOf<String>()
    private var isMiniAtm = false

    abstract fun getLink(): String?
    abstract fun getTitleText(): String?
    abstract fun getDeeplinkScheme(): String?
    abstract fun allowDebug(): Boolean
    open fun handleKycSuccess(kycSuccessFrom: KYC_SUCCESS) {

    }
    open fun handleKycSuccess(kycFlowConst: String?) {

    }

    enum class KYC_SUCCESS {
        BASIC_QRIS, BASIC_KYC, BMU_RE_KYC, KYC_EDC_ORDER_DETAIL, ADDITIONAL_DOC_KYC, ADDITIONAL_DOC_QRIS, IS_LENDING, IS_BNPL,
        BASIC_QRIS_VIDEO, ADDITIONAL_DOC_KYC_VIDEO, IS_BNPL_REGISTRATION_COMMERCE, IS_BNPL_REGISTRATION_PPOB
    }

    open fun hideToolBar(): Boolean {
        return false
    }

    private var inputIds: String? = null
    private var isFromQris: Boolean? = null
    private var ktpName: String? = null
    private var currentUrl: String? = null
    private var bankName: String? = null
    private var bankLogo: String? = null
    private var accountNumber: String? = null
    private var accountName: String? = null

    private var tempCameraType: String? = null
    private var kycFlow: String? = null
    private var reKycType: String? = null
    var eventProps: String? = ""
    private var mFilePathCallback: ValueCallback<Array<Uri>>? = null
    private val chooserIntent = Intent(Intent.ACTION_CHOOSER)
    private val galleryIntent by lazy {
        Intent(
            Intent.ACTION_PICK,
            MediaStore.Images.Media.EXTERNAL_CONTENT_URI
        )
    }
    private val locationPermission = Manifest.permission.ACCESS_FINE_LOCATION
    private val locationRequest: LocationRequest = LocationRequest.create().apply {
        interval = Constant.DEFAULT_INTERVAL
        fastestInterval = Constant.DEFAULT_FASTEST_INTERVAL
        priority = Constant.DEFAULT_PRIORITY
        maxWaitTime = Constant.DEFAULT_MAX_WAIT_TIME
    }
    private var locationRequestStartedTimeInMillis = 0L
    private var locationFoundTimeInMillis = 0L
    private val cancellationTokenSource = CancellationTokenSource()
    private var hasOpenedSettingsForLocationPermission: Boolean = false
    private var dialog: FetchingLocationDialog? = null
    private var downloadComplete = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            if (intent.action == DownloadManager.ACTION_DOWNLOAD_COMPLETE) {
                intent.extras?.let {
                    //retrieving the file
                    val downloadedFileId = it.getLong(DownloadManager.EXTRA_DOWNLOAD_ID)
                    val downloadManager =
                        getSystemService(Context.DOWNLOAD_SERVICE) as DownloadManager
                    val uri = downloadManager.getUriForDownloadedFile(downloadedFileId)

                    //opening it
                    showSnackbarToOpenDownloadedFile(uri)
                    webView?.loadUrl("javascript:downloadCompleted()")
                }
            }
        }
    }
    private lateinit var viewModel: CameraKycV2ViewModel
    private lateinit var geoCodingVM: GeocodingViewModel

    open fun getAppToken(): String? {
        return null
    }

    open fun getJanusUrl(): String? {
        return null
    }

    open fun getAccountVerificationUrl(): String? {
        return null
    }

    open fun getAuthUrl(): String? {
        return null
    }

    open fun getSessionToken(): String? {
        return null
    }

    open fun getFileSizeLimits(): FileSizeLimits {
        return FileSizeLimits()
    }

    open fun getEDCDevice(): DeviceDetails {
        return DeviceDetails()
    }

    open fun getUploadValidations(): UploadsValidations {
        return UploadsValidations()
    }

    open fun performImageValidations(): Boolean {
        return false
    }

    open fun shouldApplyNewCompression(): Boolean {
        return false
    }

    open fun getPrivyCredentials(): PrivyCredentials? {
        return null
    }

    open fun getEnv(): String {
        return Constant.DEV_ENV
    }

    open fun getImageQuality(): Int {
        return Constant.IMAGE_QUALITY_DEFAULT
    }

    open fun getVideoQuality(): String {
        return Constant.SD
    }

    open fun getCompressionQuality(): String {
        return Constant.LOW
    }

    open fun getLuminosityCheck(): Boolean {
        return false
    }

    open fun getMinLuminosity(): Float {
        return 0f
    }

    open fun getPhotoCompressionSize(): Float {
        return 1536f
    }

    open fun getUserId(): String? {
        return ""
    }

    open fun getWebviewUserUUID(): String? {
        return null
    }

    open fun restartKyc() {

    }

    open fun getKycRedirectionData(): HashMap<String, String> {
        return hashMapOf()
    }

    override fun webGetUserId() {
        webView?.loadUrl("javascript:getUserIDCallback('${getUserId()}')")
    }

    override fun showError(isServiceError: Boolean, message: String?) {
        ErrorBottomSheet.createInstance(isServiceError, message)
            .show(supportFragmentManager, ErrorBottomSheet.TAG)
    }

    override fun webViewGetUserUUID() {
        webView?.loadUrl("javascript:getUserUUIDCallback('${getWebviewUserUUID()}')")
    }


    override fun webviewRefreshToken(): String? {
        // implement in app webviewactivity side
        return null
    }

    override fun handleBackPress() {
        webView?.loadUrl("javascript:getAndroidBackPressed()")
        if (webView?.canGoBack()!!) {
            webView?.goBack()
            return
        }
        super.onBackPressed()
    }

    override fun webviewGetToken() {
        webView?.loadUrl("javascript:getTokenCallback('${getAppToken()}')")
    }

    override fun launchActivityForResult(requestType: String) {
        // implement in app webviewactivity side
    }

    override fun onQrisSubmit(result: String, bookId: String) {
        // implement in app webviewactivity side
    }

    override fun selectBankAccount(isForQris: Boolean, setQrisBank: Boolean) {
        // implement in app webviewactivity side
    }

    override fun selectQrisBankAccount(bookId: String) {
        // implement in app webviewactivity side
    }

    override fun addQrisBankAccount(bookId: String, addingFor: String, addingNewBank: Boolean) {
        // implement in app webviewactivity side
    }

    override fun downloadFile(url: String, fileName: String) {
        val request = DownloadManager.Request(Uri.parse(url))
        request.allowScanningByMediaScanner()
        request.setNotificationVisibility(DownloadManager.Request.VISIBILITY_VISIBLE_NOTIFY_COMPLETED)
        request.setDestinationInExternalPublicDir(Environment.DIRECTORY_DOWNLOADS, fileName)
        (getSystemService(DOWNLOAD_SERVICE) as DownloadManager).enqueue(request)
    }

    private fun showSnackbarToOpenDownloadedFile(path: Uri?) {
        if (path == null) return
        Snackbar.make(
            findViewById(android.R.id.content),
            getString(R.string.lib_file_download_success_open), 5000
        ).setAction(R.string.lib_open) {
            val intent = Intent(Intent.ACTION_VIEW, path).apply {
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                addFlags(Intent.FLAG_ACTIVITY_NO_HISTORY)
            }
            try {
                startActivity(intent)
            } catch (ex: ActivityNotFoundException) {
                Toast.makeText(this, getString(R.string.lib_file_app_not_found), Toast.LENGTH_SHORT)
                    .show()
            }
        }.setActionTextColor(ContextCompat.getColor(this, R.color.colorPrimary)).show()
    }

    override fun getAppVersion() {
        // implement in app webviewactivity side
    }

    override fun hideKeyboard() {
        var view = currentFocus
        //If no view currently has focus, create a new one, just so we can grab a window token from it
        if (view == null) {
            view = View(this)
        }
        hideKeyboardFrom(this, view)
    }

    override fun getBnplBookId(): String {
//        webView?.loadUrl("javascript:getTokenCallback('${getLendingId()}')")
        return "Not Found"
    }

    override fun getBnplBookName(): String {
//        webView?.loadUrl("javascript:getTokenCallback('${getLendingBookName()}')")
        return "Not Found"
    }

    override fun setFeatureShown(id: String) {

    }

    override fun isFeatureShown(id: String): String {
        return false.toString()
    }

    override fun getBWBookId(): String? {
        return null
    }

    override fun getBWBookName(): String? {
        return null
    }

    override fun getSessionValueByKey(id: String): String? {
        return null
    }

    override fun getSharedPrefValue(id: String, type: String): String? {
        return null
    }


    override fun openKeyboard() {
        (getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager).toggleSoftInput(
            2,
            0
        )
    }

    open fun hideKeyboardFrom(context: Context?, view: View?) {
        if (view == null || context == null) return
        try {
            val imm = context.getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager
            imm.hideSoftInputFromWindow(view.windowToken, 0)
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }
    }

    override fun callIntent(url: String?) {
        val intent = Intent()
        intent.action = Intent.ACTION_VIEW
        intent.data = Uri.parse(url)
        if (intent.resolveActivity(packageManager) != null) {
            startActivity(intent)
        }
    }

    open fun getToolbarColor(): Int? {
        return R.color.colorPrimary
    }

    open fun getUserAgent(): String? {
        return null
    }

    open fun getAppsflyerId(): String? {
        return null
    }

    override fun onBackPressed() {
        if (Utils.isBwUrl(currentUrl)) {
            webView?.loadUrl("javascript:onBackPressed()")
        } else if (webView?.canGoBack() == true) {
            webView?.goBack()
        } else super.onBackPressed()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_webview)
        if (Build.VERSION.SDK_INT >= 33 && applicationInfo.targetSdkVersion >= 33) {
            registerReceiver(downloadComplete, IntentFilter(DownloadManager.ACTION_DOWNLOAD_COMPLETE), Context.RECEIVER_EXPORTED)
        } else {
            registerReceiver(downloadComplete, IntentFilter(DownloadManager.ACTION_DOWNLOAD_COMPLETE))
        }
        try {
            try {
                webView = findViewById<View>(R.id.webView) as WebView?
                webView ?: return
                getLink() ?: return
                backImage = findViewById(R.id.backBtn)
                pbLoading = findViewById(R.id.pbLoading)
                titleTv = findViewById(R.id.title)
                titleTv?.text = getTitleText()
                toolbar = findViewById(R.id.toolbar)
                if (getToolbarColor() != null) {
                    toolbar?.setBackgroundResource(getToolbarColor()!!)
                } else {
                    toolbar?.setBackgroundResource(R.color.colorPrimary)
                }
                currentUrl = getLink()
                if (Utils.isBwUrl(currentUrl)) toolbar?.visibility = View.GONE
                else toolbar?.visibility = View.VISIBLE
                webView?.webViewClient = DefaultWebViewClient(
                    this,
                    getDeeplinkScheme(),
                    getAppToken(),
                    getUserId(),
                    getAppsflyerId(),
                    this
                )
                webView?.webChromeClient = DefaultChromeClient()
                webView?.settings?.javaScriptEnabled = true
                webView?.settings?.allowContentAccess = true
                webView?.settings?.allowFileAccess = true
                if (allowDebug()) {
                    WebView.setWebContentsDebuggingEnabled(true)
                }
                if (hideToolBar()) {
                    toolbar?.visibility = View.GONE
                }
                webView?.addJavascriptInterface(
                    BwWebInterface(WeakReference(this), this),
                    "BukuWebContainer"
                )
                webView?.settings?.domStorageEnabled = true
                webView?.settings?.builtInZoomControls = true
                webView?.settings?.displayZoomControls = false
                if (getUserAgent() != null) {
                    webView?.settings?.userAgentString = getUserAgent()
                }
                webView?.loadUrl(getLink()!!)
                backImage?.setImageResource(R.mipmap.close_white)
                backImage?.setOnClickListener {
                    finish()
                }
            } catch (e: Exception) {
                val browserIntent = Intent(Intent.ACTION_VIEW, Uri.parse(getLink()))
                startActivity(browserIntent)
                onCatchException(e)
            }
        } catch (e: Exception) {
            onCatchException(e)
        }
        viewModel = ViewModelProvider(this).get(CameraKycV2ViewModel::class.java)
        geoCodingVM = ViewModelProvider(this).get(GeocodingViewModel::class.java)
        viewModel.setBaseUrl(getJanusUrl())
        viewModel.setAccountVerificationUrl(getAccountVerificationUrl())
        lifecycle.addObserver(viewModel)
        viewModel.event.observe(this) {
            when (it) {
                is CameraKycV2ViewModel.Event.ApiError -> {
                    handleApiError(it.isServerError, it.errorMessage)
                }
                is CameraKycV2ViewModel.Event.Loading -> {
                    pbLoading?.apply {
                        visibility = it.isLoading.asVisibility()
                    }
                }
                CameraKycV2ViewModel.Event.NetworkError -> {
                    handleApiError(false, getString(R.string.lib_no_connection_message))
                }
                is CameraKycV2ViewModel.Event.SelfieUploadSuccess -> {
                    logUploadSuccessEvent()
                    // If liveness failed, show a dialog to restart KYC journey
                    if (it.livenessResult.isTrue) {
                        when (tempCameraType) {
                            Constant.TYPE_BASIC_KYC -> handleKycResult(KYC_SUCCESS.BASIC_KYC)
                            Constant.TYPE_BMU_RE_KYC -> handleKycResult(KYC_SUCCESS.BMU_RE_KYC)
                            Constant.TYPE_KYC_EDC_ORDER_DETAIL -> handleKycResult(KYC_SUCCESS.KYC_EDC_ORDER_DETAIL)
                            Constant.TYPE_BASIC_QRIS -> handleKycResult(KYC_SUCCESS.BASIC_QRIS)
                            Constant.TYPE_KYC_W_DOCS -> handleKycResult(KYC_SUCCESS.ADDITIONAL_DOC_KYC)
                            Constant.TYPE_QRIS_W_DOCS -> handleKycResult(KYC_SUCCESS.ADDITIONAL_DOC_QRIS)
                            Constant.TYPE_IS_LENDING -> handleKycResult(KYC_SUCCESS.IS_LENDING)
                            Constant.TYPE_IS_BNPL_REGISTRATION_PPOB -> handleKycResult(KYC_SUCCESS.IS_BNPL_REGISTRATION_PPOB)
                            Constant.TYPE_IS_BNPL_REGISTRATION_COMMERCE -> handleKycResult(
                                KYC_SUCCESS.IS_BNPL_REGISTRATION_COMMERCE
                            )
                            Constant.TYPE_IS_BNPL -> handleKycResult(KYC_SUCCESS.IS_BNPL)
                            else -> {
                                // Check if remote config has this flow constant
                                if (getKycRedirectionData().contains(tempCameraType)) {
                                    handleKycSuccess(tempCameraType)
                                }
                            }
                        }
                    } else {
                        showLivenessFailedDialog()
                    }
                }
                is CameraKycV2ViewModel.Event.KycProviderReceived -> {
                    tempCameraType?.let { type ->
                        val recommendedRes = ImageUtils.getRecommendedResolution(
                            getUploadValidations().SELFIE?.recommendedResolutionForOS
                        )
                        when (it.kycProvider) {
                            KYCProvider.PRIVY -> {
                                launchPrivy()
                            }
                            else -> {
                                startActivityForResult(
                                    CameraKycV2Activity.createIntent(
                                        this, type,
                                        getAppToken(), getJanusUrl(), getLuminosityCheck(),
                                        getMinLuminosity(), getPhotoCompressionSize(),
                                        getAuthUrl(), getSessionToken(),
                                        getFileSizeLimits().SELFIE, getImageQuality(),
                                        resolutionWidth = recommendedRes?.width,
                                        resolutionHeight = recommendedRes?.height,
                                        eventProps = eventProps,
                                        kycProvider = it.kycProvider,
                                        kycFlow = kycFlow,
                                        reKycType = reKycType,
                                        isMiniAtm = isMiniAtm
                                    ), RC_CAMERA
                                )
                            }
                        }
                    }
                }
                CameraKycV2ViewModel.Event.KycProviderMismatch -> {
                    showKycProviderMismatchError()
                }
            }
        }

        geoCodingVM.observeEvent.observe(this) {
            when(it) {
                is GeocodingViewModel.Event.AddressFound -> {
                    locationFoundTimeInMillis = System.currentTimeMillis()
                    handlePhysicalVisitAddress(it.address)
                    showFetchingLocationDialog(false)
                }

                is GeocodingViewModel.Event.AddressError -> {
                    finish()
                    trackLocationEvent(
                        gotLocation = false,
                        grantedLocationPermission = true,
                        gotLocationFailureReason = it.exception.message
                    )
                }
            }
        }

        kycFlowConstants.addAll(newKycFlowConstants)
        kycFlowConstants.addAll(getKycRedirectionData().keys)
    }

    override fun onResume() {
        super.onResume()
        if (hasOpenedSettingsForLocationPermission) {
            hasOpenedSettingsForLocationPermission = false
            if (ContextCompat.checkSelfPermission(this, locationPermission) == PackageManager.PERMISSION_GRANTED) {
                if (isLocationEnabled()) {
                    getCurrentLocation()
                } else {
                    startLocationCallback()
                }
            } else {
                showLocationNotDetectedBottomSheet()
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        unregisterReceiver(downloadComplete)
    }

    // to be overridden
    private fun onCatchException(e: Exception) {
        Utils.logException(this, e)
    }

    private fun handleApiError(isServerError: Boolean, errorMessage: String) {
        ErrorBottomSheet.createInstance(isServerError, errorMessage)
            .show(supportFragmentManager, ErrorBottomSheet.TAG)
    }

    override fun onSetTitle(text: String?) {
        titleTv?.text = text
    }

    override fun sendAppsflyerEvent(eventName: String, jsonProp: String?) {}

    override fun sendMoengageEvent(eventName: String, jsonProp: String?) {}

    override fun onTokokoEvent(eventName: String, jsonProp: String?) {
    }

    override fun openCamera(type: String?) {
        if (pbLoading?.isVisible.isTrue) {
            // Return in case we are uploading selfies and user clicks the CTA on mweb again
            return
        }
        tempCameraType = type
        if (allPermissionsGranted()) {
            if (!type.isNullOrEmpty() && kycFlowConstants.contains(type)) {
                viewModel.getKycProvider(
                    authToken = getAppToken(),
                    authUrl = getAuthUrl().orEmpty(),
                    sessionToken = getSessionToken().orEmpty(),
                    isMiniAtm = isMiniAtm
                )
            } else {
                startActivityForResult(
                    CameraKycActivity.createIntent(
                        this, type, checkLuminosity = getLuminosityCheck(),
                        minLuminosity = getMinLuminosity(),
                        isMiniAtm = isMiniAtm
                    ), RC_CAMERA)
            }
        } else {
            ActivityCompat.requestPermissions(this, REQUIRED_PERMISSIONS, REQUEST_CODE_PERMISSIONS)
        }
    }

    private fun logUploadSuccessEvent() {
        val props = mutableMapOf<String?, Any?>()
        props[AnalyticsUtil.STATUS] = AnalyticsUtil.SUCCESS
        AnalyticsUtil.addExtraEventProps(eventProps, props)
        AnalyticsUtil.logEvent(
            this,
            AnalyticsUtil.EVENT_SELFIE_UPLOAD_COMPLETED,
            AnalyticsUtil.getJsonStringOfMap(props)
        )
    }

    /**
     * Privy didn't share dev credentials, so we are using same for dev and staging
     */
    private fun getPrivyEnv(): String {
        return when (getEnv()) {
            Constant.DEV_ENV -> PrivypassEnv.STAGING
            Constant.STAGING_ENV -> PrivypassEnv.STAGING
            Constant.PROD_ENV -> PrivypassEnv.PRODUCTION
            else -> PrivypassEnv.STAGING
        }
    }

    private fun launchPrivy() {
        Utils.safeLet(
            getPrivyCredentials()?.username,
            getPrivyCredentials()?.password,
            getPrivyCredentials()?.applicationID,
            getPrivyCredentials()?.merchantKey
        ) { userName, password, applicationID, merchantKey ->
            PrivypassLivenessBuilder.instance
                .setUsername(userName)
                .setPassword(password)
                .setApplicationID(applicationID)
                .setMerchantKey(merchantKey)
                .setENV(getPrivyEnv())
                .setCloseOnFailedVerification(true)
                .startCamera(this,
                    object : PrivypassLivenessBuilder.CallbackCamera {
                        override fun onResult(
                            result: Boolean, fcToken: String,
                            image1: String, image2: String, message: String
                        ) {
                            if (fcToken.isNotEmpty()) {
                                // Liveness test was performed by Privy
                                if (image1.isNotEmpty() && image2.isNotEmpty()) {
                                    viewModel.uploadPrivyImages(
                                        image1, image2,
                                        getAppToken(), getAuthUrl().orEmpty(),
                                        getSessionToken().orEmpty(),
                                        result, fcToken, KYCProvider.PRIVY
                                    )
                                } else {
                                    Toast.makeText(
                                        this@BaseWebviewActivity,
                                        getString(R.string.aw_empty_images_received),
                                        Toast.LENGTH_SHORT
                                    ).show()
                                }
                            }
                        }
                    })
        } ?: run {
            Toast.makeText(
                this, getString(R.string.aw_missing_credentials), Toast.LENGTH_SHORT
            ).show()
        }
    }

    private fun showKycProviderMismatchError() {
        GenericDialog.create(context = this) {
            titleRes = R.string.aw_kyc_provider_error_title
            bodyRes = R.string.aw_kyc_provider_error_message
            btnRightRes = R.string.lib_retake_photo
            btnLeftRes = null
            rightBtnCallback = {
                viewModel.getKycProvider(
                    authToken = getAppToken(),
                    authUrl = getAuthUrl().orEmpty(),
                    sessionToken = getSessionToken().orEmpty(),
                    isMiniAtm = isMiniAtm
                )
            }
        }.show()
    }

    private fun showLivenessFailedDialog() {
        GenericDialog.create(context = this) {
            titleRes = R.string.aw_liveness_failure_title
            bodyRes = R.string.aw_liveness_failure_message
            btnLeftRes = R.string.aw_exit
            btnRightRes = R.string.aw_yes
            rightBtnCallback = { restartKyc() }
            leftBtnCallback = { finish() }
        }.show()
    }

    override fun openCameraWithParams(type: String?, title: String?, message: String?) {
        tempCameraType = type
        if (allPermissionsGranted()) {
            startActivityForResult(
                CameraKycActivity.createIntent(
                    this, type, title, message, checkLuminosity = getLuminosityCheck(),
                    minLuminosity = getMinLuminosity(),
                    isMiniAtm = isMiniAtm
                ),
                RC_CAMERA
            )
        } else {
            ActivityCompat.requestPermissions(this, REQUIRED_PERMISSIONS, REQUEST_CODE_PERMISSIONS)
        }
    }

    override fun openCameraWithProps(type: String?, props: String?) {
        tempCameraType = type
        eventProps = props
        if (type == TYPE_BMU_RE_KYC) {
            kycFlow = "RE_KYC"
            reKycType = "BMU_LIMIT_ACTIVATION"
        }
        val propsObject = JSONObject(props)
        isMiniAtm = propsObject.optBoolean("isMiniAtm")
        openCamera(type)
    }

    private fun getSizeLimit(type: String?): Long {
        return when (type) {
            Constant.TYPE_KTP, Constant.TYPE_SELFIE_KTP -> getFileSizeLimits().KTP
            Constant.TYPE_MH_TICKET -> getFileSizeLimits().MH_TICKET_IMAGE
            else -> getFileSizeLimits().OTHERS
        }
    }

    override fun openVideoKyc(inputIds: String, isFromQris: Boolean, ktpName: String) {
        if (allPermissionsGranted()) {
            startActivityForResult(
                VideoKycActivity.createIntent(
                    this, inputIds, isFromQris, getAppToken(), getJanusUrl(), ktpName,
                    getAuthUrl(), getSessionToken(), getFileSizeLimits().VIDEO,
                    videoQuality = getVideoQuality(),
                    compressionQuality = getCompressionQuality()
                ), RC_VIDEO
            )
        } else {
            this.inputIds = inputIds
            this.isFromQris = isFromQris
            this.ktpName = ktpName
            ActivityCompat.requestPermissions(
                this,
                REQUIRED_PERMISSIONS_VIDEO,
                REQUEST_CODE_PERMISSIONS_VIDEO
            )
        }
    }

    override fun openVideoKycWithProps(
        input: String, isFromQris: Boolean, ktpName: String, props: String?
    ) {
        eventProps = props
        openVideoKyc(input, isFromQris, ktpName)
    }

    override fun startLivelinessCheck(productId: String?) {}
    override fun initializeLivelinessCheck(userCase: String?) {}

    override fun openImagePDFPicker() {
        if (Build.VERSION.SDK_INT in 23..31 && !hasStoragePermission()) {
            ActivityCompat.requestPermissions(
                this@BaseWebviewActivity,
                REQUIRED_PERMISSIONS_FILE,
                GALLERY_EXTERNAL_STORAGE
            )
        } else {
            startActivityForResult(
                getFileChooserIntent(),
                RC_FILE_PICKER
            )
        }
    }

    private fun getFileChooserIntent(): Intent {
        val mimeTypes = arrayOf("image/*", "application/pdf")
        val MAX_FILE_SIZE = 2 // in MB
        val intent = Intent(Intent.ACTION_GET_CONTENT)
        intent.addCategory(Intent.CATEGORY_OPENABLE)
        intent.putExtra(MediaStore.EXTRA_SIZE_LIMIT, MAX_FILE_SIZE)

        intent.type = if (mimeTypes.size == 1) mimeTypes[0] else "*/*"
        if (mimeTypes.isNotEmpty()) {
            intent.putExtra(Intent.EXTRA_MIME_TYPES, mimeTypes)
        }

        intent.putExtra(Intent.EXTRA_TITLE, getString(R.string.select_document))
        return intent
    }

    override fun openGallery() {
        if (Build.VERSION.SDK_INT in 23..31 && !hasStoragePermission()) {
            ActivityCompat.requestPermissions(
                this@BaseWebviewActivity,
                REQUIRED_PERMISSIONS_FILE,
                GALLERY_EXTERNAL_STORAGE
            )
        } else {
            startActivityForResult(
                Intent.createChooser(galleryIntent, "select image"),
                RC_GALLERY
            )
        }
    }

    override fun openRefundBank() {
        val intent =
            Intent(this, Class.forName(REFUND_CLASS_NAME))
        intent.apply {
            putExtra("entryPoint", "in-app ticket")
            putExtra("IS_REFUND", true)
            putExtra("paymentType", "4")
        }
        startActivityForResult(intent, RC_REFUND_BANK_SELECTED)
    }

    private fun allPermissionsGranted() = REQUIRED_PERMISSIONS.all {
        ContextCompat.checkSelfPermission(baseContext, it) == PackageManager.PERMISSION_GRANTED
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        when (requestCode) {
            REQUEST_CODE_PERMISSIONS -> {
                if (allPermissionsGranted()) {
                    tempCameraType?.let {
                        when {
                            kycFlowConstants.contains(it) -> {
                                viewModel.getKycProvider(
                                    authToken = getAppToken(),
                                    authUrl = getAuthUrl().orEmpty(),
                                    sessionToken = getSessionToken().orEmpty(),
                                    isMiniAtm = isMiniAtm
                                )
                            }
                            lendingConstants.contains(it) -> {
                                startActivityForResult(
                                    CameraKycActivity.createIntent(
                                        this,
                                        tempCameraType,
                                        checkLuminosity = getLuminosityCheck(),
                                        minLuminosity = getMinLuminosity(),
                                        isMiniAtm = isMiniAtm
                                    ), RC_CAMERA
                                )
                            }
                            else -> {
                                startActivityForResult(
                                    CameraKycActivity.createIntent(
                                        this,
                                        tempCameraType,
                                        checkLuminosity = getLuminosityCheck(),
                                        minLuminosity = getMinLuminosity(),
                                        isMiniAtm = isMiniAtm
                                    ), RC_CAMERA
                                )
                            }
                        }
                    } ?: run {
                        startActivityForResult(
                            CameraKycActivity.createIntent(
                                this,
                                tempCameraType,
                                checkLuminosity = getLuminosityCheck(),
                                minLuminosity = getMinLuminosity(),
                                isMiniAtm = isMiniAtm
                            ), RC_CAMERA
                        )
                    }
                }
            }
            REQUEST_CODE_PERMISSIONS_VIDEO -> {
                if (allPermissionsGranted()) {
                    startActivityForResult(
                        VideoKycActivity.createIntent(
                            this, inputIds, isFromQris, getAppToken(), getJanusUrl(), ktpName,
                            getAuthUrl(), getSessionToken(), getFileSizeLimits().VIDEO,
                            videoQuality = getVideoQuality(),
                            compressionQuality = getCompressionQuality()
                        ),
                        RC_VIDEO
                    )
                }
            }
            WRITE_EXTERNAL_STORAGE -> {
                if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    startActivityForResult(chooserIntent, INPUT_FILE_REQUEST_CODE)
                }
            }
            GALLERY_EXTERNAL_STORAGE -> {
                if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    startActivityForResult(
                        Intent.createChooser(galleryIntent, "select image"),
                        RC_GALLERY
                    )
                }
            }
            REQUEST_CODE_LOCATION -> {
                if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    startLocationCallback()
                } else {
                    trackLocationEvent(
                        gotLocation = false,
                        grantedLocationPermission = false,
                        gotLocationFailureReason = AnalyticsUtil.PERMISSION_DENIED
                    )
                    showLocationNotDetectedBottomSheet()
                }
            }
        }
    }

    override fun onSuccess(type: String?) {}

    override fun redirectToPaymentDetail(
        orderId: String,
        paymentType: String,
        isSuccess: Boolean
    ) {
    }

    override fun onSuccessWithMessage(type: String?, message: String?) {}

    override fun redirectPaymentDetail(
        orderId: String,
        paymentType: String,
        isSuccess: Boolean,
        message: String,
        isFromAssistPage: Boolean
    ) {
    }

    override fun onCloseWebview() {
        finish()
    }

    override fun startPhysicalVisit() {
        // Request Location permission
        // If location permission is granted, check whether this comes under servable area.
        getLocationPermission(this::startLocationCallback, REQUEST_CODE_LOCATION)
    }

    private fun startLocationCallback() {
        checkLocationService(this::getCurrentLocation, REQUEST_CODE_GPS)
    }

    private fun getCurrentLocation() {
        showFetchingLocationDialog(true)
        locationRequestStartedTimeInMillis = System.currentTimeMillis()
        getCurrentFusedLocation(cancellationTokenSource) {
            geoCodingVM.geocodeAddress(it.latitude, it.longitude)
        }
    }

    override fun requestLocation() {
        // implement in app webviewactivity side
    }

    override fun redirectVidaSign(url: String?) {
        // implement in app webviewactivity side
    }

    private fun showLocationNotDetectedBottomSheet() {
        LocationNotDetectedBottomSheet.createIntent(
            isCancellable = false,
            useCase = LocationNotDetectedBottomSheet.UseCase.LOCATION_NOT_DETECTED_PHYSICAL_VISIT
        ).show(supportFragmentManager, LocationNotDetectedBottomSheet.TAG)
    }

    private fun showFetchingLocationDialog(show: Boolean) {
        if (show) {
            dialog = FetchingLocationDialog(this)
            dialog?.show()
        } else {
            dialog?.dismiss()
        }
    }

    private fun handlePhysicalVisitAddress(address: String) {
        // check whether this comes under servable area.
        val addressJson: JsonObject? = Gson().fromJson(address, JsonObject::class.java)
        val cityLocation = addressJson?.get("city")?.asString.orEmpty()
        if (isAddressWithinVisitRange(address)) {
            addressJson?.addProperty(Constant.USE_CASE, Constant.PHYSICAL_VISIT)
            webView?.loadUrl("javascript:selectAddressCallback('${Gson().toJson(addressJson)}')")
            trackLocationEvent(
                cityInRange = true,
                cityLocation = cityLocation,
                address = address
            )
        } else {
            LocationNotDetectedBottomSheet.createIntent(
                isCancellable = false,
                useCase = LocationNotDetectedBottomSheet.UseCase.LOCATION_OUT_OF_RANGE
            ).show(supportFragmentManager, LocationNotDetectedBottomSheet.TAG)
            trackLocationEvent(
                cityInRange = false,
                cityLocation = cityLocation
            )
        }
    }

    private fun trackLocationEvent(
        grantedLocationPermission: Boolean = true,
        gotLocation: Boolean = true,
        gotLocationFailureReason: String? = null,
        cityInRange: Boolean = false,
        cityLocation: String = "",
        address: String = ""
    ) {
        val props = mutableMapOf<String?, Any?>()
        props[AnalyticsUtil.GET_LOCATION_PERMISSION] = grantedLocationPermission.toAnalyticsYesOrNo
        props[AnalyticsUtil.GET_LOCATION] = gotLocation.toAnalyticsSuccessOrFailure
        props[AnalyticsUtil.GET_LOCATION_FAILED_REASON] = gotLocationFailureReason
        props[AnalyticsUtil.GET_LOCATION_TIME_IN_SECONDS] = (locationFoundTimeInMillis - locationRequestStartedTimeInMillis)/1000
        props[AnalyticsUtil.CITY_IN_SALES_AREA] = cityInRange.toAnalyticsYesOrNo
        props[AnalyticsUtil.CITY_LOCATION] = cityLocation
        props[AnalyticsUtil.ADDRESS] = address
        trackEvent(
            AnalyticsUtil.EVENT_REQUEST_PHY_VER_LOCATION,
            AnalyticsUtil.getJsonStringOfMap(props)
        )
    }

    open fun isAddressWithinVisitRange(address: String): Boolean {
        return false
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        when (requestCode) {
            RC_LOCATION_AND_IMAGE -> {
                handleLocationResult(data)
                handleCameraResult(data)
            }
            RC_CAMERA -> {
                if (resultCode != Activity.RESULT_OK) return
                val kycProviderMismatch = data?.getBooleanExtra(KYC_PROVIDER_MISMATCH, false)
                if (kycProviderMismatch == true) {
                    viewModel.getKycProvider(
                        authToken = getAppToken(),
                        authUrl = getAuthUrl().orEmpty(),
                        sessionToken = getSessionToken().orEmpty(),
                        isMiniAtm = isMiniAtm
                    )
                    return
                }
                when (tempCameraType) {
                    Constant.TYPE_BASIC_KYC -> handleKycResult(KYC_SUCCESS.BASIC_KYC)
                    Constant.TYPE_BMU_RE_KYC -> handleKycResult(KYC_SUCCESS.BMU_RE_KYC)
                    Constant.TYPE_KYC_EDC_ORDER_DETAIL -> handleKycResult(KYC_SUCCESS.KYC_EDC_ORDER_DETAIL)
                    Constant.TYPE_BASIC_QRIS -> handleKycResult(KYC_SUCCESS.BASIC_QRIS)
                    Constant.TYPE_KYC_W_DOCS -> handleKycResult(KYC_SUCCESS.ADDITIONAL_DOC_KYC)
                    Constant.TYPE_QRIS_W_DOCS -> handleKycResult(KYC_SUCCESS.ADDITIONAL_DOC_QRIS)
                    Constant.TYPE_IS_LENDING -> handleKycResult(KYC_SUCCESS.IS_LENDING)
                    Constant.TYPE_IS_BNPL_REGISTRATION_PPOB -> handleKycResult(KYC_SUCCESS.IS_BNPL_REGISTRATION_PPOB)
                    Constant.TYPE_IS_BNPL_REGISTRATION_COMMERCE -> handleKycResult(KYC_SUCCESS.IS_BNPL_REGISTRATION_COMMERCE)
                    Constant.TYPE_IS_BNPL -> handleKycResult(KYC_SUCCESS.IS_BNPL)
                    else -> {
                        // Check if remote config has this flow constant
                        if (getKycRedirectionData().contains(tempCameraType)) {
                            handleKycSuccess(tempCameraType)
                        } else {
                            handleCameraResult(data)
                        }
                    }
                }
            }
            RC_VIDEO -> {
                if (resultCode != Activity.RESULT_OK) return
                val isFromQris =
                    data?.extras?.getBoolean(VideoKycActivity.FROM_QRIS, false) ?: false
                val kycSuccessFrom =
                    if (isFromQris) KYC_SUCCESS.BASIC_QRIS_VIDEO else KYC_SUCCESS.ADDITIONAL_DOC_KYC_VIDEO
                handleKycResult(kycSuccessFrom)
            }
            RC_GALLERY -> {
                if (resultCode != Activity.RESULT_OK) return
                handleGalleryResult(data)
            }
            RC_FILE_PICKER -> {
                if (resultCode != Activity.RESULT_OK) return
                handlePDFGalleryResult(data)
            }
            INPUT_FILE_REQUEST_CODE -> {
                var results: Array<Uri>? = null
                if (resultCode == Activity.RESULT_OK) {
                    val dataString = data?.dataString
                    if (dataString != null) {
                        results = arrayOf(Uri.parse(dataString)) // Uri of uploaded file
                    }
                    mFilePathCallback?.onReceiveValue(results)
                } else {
                    mFilePathCallback?.onReceiveValue(null)
                }
                mFilePathCallback = null
            }
            RC_PIN_SELECT -> {
                if (resultCode != Activity.RESULT_OK) return
                webView?.loadUrl("javascript:bankDetailsCallback('${bankName}', '$bankLogo', '$accountNumber', '$accountName')")
            }
            RC_REFUND_BANK_SELECTED -> {
                if (resultCode != Activity.RESULT_OK) return
                if (data?.getBooleanExtra("add_bank", false) == true) {
                    val intent =
                        Intent(
                            this,
                            Class.forName("com.bukuwarung.payments.pin.NewPaymentPinActivity")
                        )
                    intent.putExtra("usecase", "PIN_CONFIRM")
                    startActivityForResult(intent, RC_PIN_ADD_BANK)
                } else {
                    bankName = data?.getStringExtra("bank_name")
                    bankLogo = data?.getStringExtra("bank_logo")
                    accountNumber = data?.getStringExtra("account_number")
                    accountName = data?.getStringExtra("user_name")
                    val intent =
                        Intent(
                            this,
                            Class.forName("com.bukuwarung.payments.pin.NewPaymentPinActivity")
                        )
                    intent.putExtra("usecase", "PIN_CONFIRM")
                    startActivityForResult(intent, RC_PIN_SELECT)
                }
            }
            RC_PIN_ADD_BANK -> {
                if (resultCode != Activity.RESULT_OK) return
                val intent =
                    Intent(
                        this,
                        Class.forName("com.bukuwarung.payments.addbank.AddBankAccountActivity")
                    )
                intent.putExtra("paymentType", "4")
                intent.putExtra("entryPoint", "payment_details")
                intent.putExtra("hasBankAccount", true)
                intent.putExtra("showTutorial", false)
                startActivityForResult(intent, RC_SET_REFUND_RESULT)

            }
            RC_SET_REFUND_RESULT -> {
                if (resultCode != Activity.RESULT_OK) return
                bankName = data?.getStringExtra("bank_name")
                bankLogo = data?.getStringExtra("bank_logo")
                accountNumber = data?.getStringExtra("account_number")
                accountName = data?.getStringExtra("user_name")
                webView?.loadUrl("javascript:bankDetailsCallback('${bankName}', '$bankLogo', '$accountNumber', '${accountName}')")
            }
            REQUEST_CODE_GPS -> {
                if (RESULT_OK == resultCode) {
                    getCurrentLocation()
                } else {
                    trackLocationEvent(
                        gotLocation = false,
                        grantedLocationPermission = true,
                        gotLocationFailureReason = AnalyticsUtil.GPS_DISABLED
                    )
                    showLocationNotDetectedBottomSheet()
                }
            }
        }
    }

    private fun handlePDFGalleryResult(data: Intent?) {
        data ?: return
        val selectedImageUri = data.data

        var selectedImagePath: String? =
            FileUtils(this@BaseWebviewActivity).getPath(selectedImageUri)

        if (selectedImagePath == null)
            selectedImagePath = ImageUtils.generateLocalFile(
                contentResolver,
                getOutputDirectory(),
                selectedImageUri
            )

        selectedImagePath?.let {
            val imageFile = File(it)
            val extension = ImageUtils.getFileType(imageFile)
            if (performImageValidations() && hasValidationError(
                    imageFile,
                    Constant.TYPE_MH_TICKET
                )
            ) {
                return@let
            }
            if (imageFile.exists()) {
                if (extension == "IMAGE") {
                    var fileToUpload = imageFile
                    if (shouldApplyNewCompression()) {
                        val recommendedRes = ImageUtils.getRecommendedResolution(
                            getUploadValidations(Constant.TYPE_MH_TICKET)?.recommendedResolutionForOS
                        )
                        fileToUpload = ImageUtils.compressImage(
                            this, imageFile, getSizeLimit(Constant.TYPE_MH_TICKET),
                            getImageQuality(),
                            recommendedRes?.width, recommendedRes?.height
                        )
                    } else {
                        ImageUtils.compressImage(
                            imageFile.absolutePath,
                            900f
                        )
                    }
                    val fileSize = fileToUpload.length()
                    if (fileSize > getSizeLimit(Constant.TYPE_MH_TICKET)) {
                        showMaxFileSizeError()
                    } else {
                        val b64 = bitmapFileToBase64(fileToUpload)
                        val chunked = b64.chunked(195000)
                        for (i in chunked.indices) {
                            val hasNext = if (i != chunked.size - 1) "true" else "false"
                            webView?.loadUrl("javascript:batchPDFImagePickerCallback('${chunked[i]}', 'IMAGE', $hasNext, $i)")
                        }
                    }
                } else if (extension == "PDF") {
                    val fileSize = imageFile.length()
                    if (fileSize > getFileSizeLimits().MH_TICKET_PDF) {
                        showMaxFileSizeError()
                    } else {
                        val b64 = convertToString(selectedImageUri)
                        b64?.let {
                            val chunked = b64.chunked(195000)
                            for (i in chunked.indices) {
                                val hasNext = if (i != chunked.size - 1) "true" else "false"
                                webView?.loadUrl("javascript:batchPDFImagePickerCallback('${chunked[i]}', 'PDF', $hasNext, $i)")
                            }

                        }
                            ?: webView?.loadUrl("javascript:batchPDFImagePickerCallback('Unable to convert pdf to base64', 'PDF')")
                    }
                }
            }
        }
    }

    private fun convertToString(uri: Uri?): String? {
        if (uri == null) return null
        try {
            val input = contentResolver.openInputStream(uri)
            val bytes = getBytes(input)
            return Base64.encodeToString(bytes, Base64.DEFAULT)
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }
        return null
    }

    private fun getBytes(inputStream: InputStream?): ByteArray {
        val byteBuffer = ByteArrayOutputStream()
        val bufferSize = 1024
        val buffer = ByteArray(bufferSize)
        var len = 0
        while (inputStream!!.read(buffer).also { len = it } != -1) {
            byteBuffer.write(buffer, 0, len)
        }
        return byteBuffer.toByteArray()
    }

    private fun showMaxFileSizeError() {
        GenericDialog.create(context = this) {
            titleRes = R.string.file_size_error
            bodyRes = R.string.file_size_message
            btnLeftRes = R.string.lib_batal
            btnRightRes = R.string.lib_reload
            rightBtnCallback = {
                openImagePDFPicker()
            }
        }.show()
    }

    private fun getOutputDirectory(): File {
        val mediaDir = externalMediaDirs.firstOrNull()?.let {
            File(
                it, TEMP_DIR
            ).apply { mkdirs() }
        }
        return if (mediaDir != null && mediaDir.exists())
            mediaDir else filesDir
    }

    /**
     * Get applicable validations for the type of file upload.
     */
    private fun getUploadValidations(type: String?): UploadsValidation? {
        return when (type) {
            Constant.TYPE_KTP, Constant.TYPE_SELFIE_KTP -> getUploadValidations().KTP
            Constant.TYPE_MH_TICKET -> getUploadValidations().MH_TICKET_IMAGE
            else -> getUploadValidations().OTHERS
        }
    }

    /**
     * Returns true if there is any validation error, false otherwise.
     */
    private fun hasValidationError(file: File, type: String?): Boolean {
        val validations = getUploadValidations(type)
        val validationError =
            ImageUtils.performImageValidations(this, file, validations, type.orEmpty())
        val errorMessage = ImageUtils.getValidationErrorMessage(this, validationError, validations)
        if (errorMessage != null) {
            SizeLimitErrorBS.createInstance(
                getSizeLimit(Constant.TYPE_MH_TICKET), errorMessage
            ).show(supportFragmentManager, SizeLimitErrorBS.TAG)
            return true
        }
        return false
    }

    private fun handleGalleryResult(data: Intent?) {
        data ?: return
        val selectedImageUri = data.data
        val selectedImagePath: String? = getRealPathFromURIForGallery(selectedImageUri)
        selectedImagePath?.let {
            val imageFile = File(it)
            if (imageFile.exists()) {
                var fileToUpload = imageFile
                if (shouldApplyNewCompression()) {
                    val recommendedRes = ImageUtils.getRecommendedResolution(
                        getUploadValidations(Constant.TYPE_MH_TICKET)?.recommendedResolutionForOS
                    )
                    fileToUpload = ImageUtils.compressImage(
                        this, imageFile, getSizeLimit(Constant.TYPE_MH_TICKET),
                        getImageQuality(),
                        recommendedRes?.width, recommendedRes?.height
                    )
                } else {
                    ImageUtils.compressImage(
                        imageFile.absolutePath,
                        900f
                    )
                }
                if (performImageValidations() && hasValidationError(
                        imageFile,
                        Constant.TYPE_MH_TICKET
                    )
                ) {
                    return@let
                }
                val b64 = bitmapFileToBase64(fileToUpload)
                val chunked = b64.chunked(195000)
                for (i in chunked.indices) {
                    val hasNext = if (i != chunked.size - 1) "true" else "false"
                    webView?.loadUrl("javascript:batchCameraCallback('${chunked[i]}', 'MH_TICKET', $hasNext, $i)")
                }
            }
        }
    }

    private fun handleKycResult(kycSuccessFrom: KYC_SUCCESS) {
        handleKycSuccess(kycSuccessFrom)
    }

    private fun handleCameraResult(data: Intent?) {
        data ?: return
        val type = data.getStringExtra(CameraKycActivity.TYPE)
        val filePath = data.getStringExtra(CameraKycActivity.FILE_PATH)
        filePath ?: return
        val file = File(filePath)
        if (file.exists()) {
            var fileToUpload = file
            if (shouldApplyNewCompression()) {
                val recommendedRes = ImageUtils.getRecommendedResolution(
                    getUploadValidations(type)?.recommendedResolutionForOS
                )
                fileToUpload = ImageUtils.compressImage(
                    this, file, getSizeLimit(type), getImageQuality(),
                    recommendedRes?.width, recommendedRes?.height
                )
            } else {
                ImageUtils.compressImage(
                    file.absolutePath,
                    when (type) {
                        Constant.TYPE_KTP -> 2048f
                        Constant.TYPE_MH_TICKET -> 900f
                        else -> 1536f
                    }
                )
            }
            if (performImageValidations() && hasValidationError(fileToUpload, type)) {
                return
            }
            val b64 = if (type == Constant.TYPE_KTP) {
                getCroppedAndBase64(
                    fileToUpload,
                    data.getIntExtra(IMAGE_X_AXIS, 1),
                    data.getIntExtra(IMAGE_Y_AXIS, 1),
                    data.getIntExtra(IMAGE_WIDTH, 1),
                    data.getIntExtra(IMAGE_HEIGHT, 1)
                )
            } else {
                bitmapFileToBase64(fileToUpload)
            }
            if (b64.isEmpty()) {
                Toast.makeText(this, R.string.aw_empty_image_retry, Toast.LENGTH_SHORT).show()
                return
            }
            val chunked = b64.chunked(195000)
            for (i in chunked.indices) {
                val hasNext = if (i != chunked.size - 1) "true" else "false"
                webView?.loadUrl("javascript:batchCameraCallback('${chunked[i]}', '$type', $hasNext, $i)")
            }
        }
    }

    private fun handleLocationResult(data: Intent?) {
        data ?: return
        val address = data.getStringExtra(Constant.ADDRESS)
        address?.let {
            webView?.loadUrl("javascript:selectAddressCallback('${address}')")
        }
    }

    private fun getCroppedAndBase64(
        file: File,
        xAxis: Int,
        yAxis: Int,
        width: Int,
        height: Int
    ): String {
        val imageOriginal = BitmapFactory.decodeStream(FileInputStream(file))
        val widthRatio = width.toFloat() / resources.displayMetrics.widthPixels.toFloat()
        val heightRatio = height.toFloat() / resources.displayMetrics.heightPixels.toFloat()
        val xAxisRatio = xAxis.toFloat() / resources.displayMetrics.widthPixels.toFloat()
        val yAxisRatio = yAxis.toFloat() / resources.displayMetrics.heightPixels.toFloat()
        val croppedBitmap = Bitmap.createBitmap(
            imageOriginal,
            (imageOriginal.width * xAxisRatio).roundToInt(),
            (imageOriginal.height * yAxisRatio).roundToInt(),
            (imageOriginal.width * widthRatio).roundToInt(),
            (imageOriginal.height * heightRatio).roundToInt()
        )
        val byteArrayOutputStream = ByteArrayOutputStream()
        croppedBitmap.compress(Bitmap.CompressFormat.JPEG, 100, byteArrayOutputStream)
        val byteArray: ByteArray = byteArrayOutputStream.toByteArray()
        return Base64.encodeToString(byteArray, Base64.DEFAULT)
    }

    private fun bitmapFileToBase64(file: File): String {
        val imageOriginal = BitmapFactory.decodeStream(FileInputStream(file))
        val byteArrayOutputStream = ByteArrayOutputStream()
        imageOriginal.compress(Bitmap.CompressFormat.JPEG, 100, byteArrayOutputStream)
        val byteArray: ByteArray = byteArrayOutputStream.toByteArray()
        return Base64.encodeToString(byteArray, Base64.DEFAULT)
    }

    companion object {
        const val LINK = "link"
        const val TITLE = "title"
        const val USER_AGENT = "userAgent"
        const val MOBILE_DEVICE = "mobile_device"
        const val KYC_PROVIDER_MISMATCH = "kyc_provider_mismatch"
        const val TEMP_DIR = "temp_dir"
        private const val RC_CAMERA = 99
        private const val RC_VIDEO = 102
        private const val RC_GALLERY = 100
        private const val RC_FILE_PICKER = 101
        private const val RC_PIN_SELECT = 98
        private const val RC_PIN_ADD_BANK = 97
        private const val RC_SET_REFUND_RESULT = 96
        private const val RC_REFUND_BANK_SELECTED = 95
        private const val RC_ADD_REFUND_BANK = 94
        private const val REQUEST_CODE_PERMISSIONS = 10
        private const val REQUEST_CODE_PERMISSIONS_VIDEO = 11
        private const val REQUEST_CODE_LOCATION = 12
        private const val REQUEST_CODE_GPS = 13
        private val REQUIRED_PERMISSIONS = arrayOf(Manifest.permission.CAMERA)
        private val REQUIRED_PERMISSIONS_VIDEO = arrayOf(
            Manifest.permission.CAMERA,
            Manifest.permission.WRITE_EXTERNAL_STORAGE,
            Manifest.permission.RECORD_AUDIO
        )
        private val REQUIRED_PERMISSIONS_FILE = arrayOf(
            Manifest.permission.READ_EXTERNAL_STORAGE,
            Manifest.permission.WRITE_EXTERNAL_STORAGE
        )
        private const val INPUT_FILE_REQUEST_CODE = 1
        private const val WRITE_EXTERNAL_STORAGE = 331
        private const val GALLERY_EXTERNAL_STORAGE = 332
        const val RC_CONTACT = 41
        const val RC_ADD_BANK_ACCOUNT = 42
        const val RC_BUSINESS_NAME_CHANGE = 43
        const val RC_ADDRESS = 44
        const val RC_LIVELINESS = 45
        const val RC_OTP_VERIFICATION = 46
        const val RC_LOCATION_AND_IMAGE = 103
        const val REQUEST_TYPE_ADDRESS = "address"
        private const val REFUND_CLASS_NAME =
            "com.bukuwarung.payments.banklist.BankAccountListActivity"
    }

    override fun onUrlChange(url: String?) {
        currentUrl = url
        if (Utils.isBwUrl(currentUrl) || hideToolBar()) toolbar?.visibility = View.GONE
        else toolbar?.visibility = View.VISIBLE
    }

    inner class DefaultChromeClient : WebChromeClient() {

        override fun onShowFileChooser(
            webView: WebView?,
            filePathCallback: ValueCallback<Array<Uri>>?,
            fileChooserParams: FileChooserParams?
        ): Boolean {
            if (mFilePathCallback != null) {
                mFilePathCallback!!.onReceiveValue(null)
            }
            mFilePathCallback = filePathCallback
            if (Build.VERSION.SDK_INT in 23..31 && !hasStoragePermission()) {
                ActivityCompat.requestPermissions(
                    this@BaseWebviewActivity,
                    arrayOf(
                        "android.permission.WRITE_EXTERNAL_STORAGE",
                        "android.permission.READ_EXTERNAL_STORAGE"
                    ),
                    WRITE_EXTERNAL_STORAGE
                )
            } else {
                showFileChooser()
            }
            return true
        }

        fun hasStoragePermission(): Boolean {
            var isGranted = true
            if (Build.VERSION.SDK_INT < 23) {
                return true
            }
            val appContext: Context = applicationContext
                ?: return false
            if (appContext.checkSelfPermission("android.permission.WRITE_EXTERNAL_STORAGE") != PackageManager.PERMISSION_GRANTED) {
                isGranted = false
            }
            return isGranted
        }
    }

    fun showFileChooser() {
        val contentSelectionIntent = Intent(Intent.ACTION_GET_CONTENT)
        contentSelectionIntent.addCategory(Intent.CATEGORY_OPENABLE)
        contentSelectionIntent.type = "*/*"
        chooserIntent.putExtra(Intent.EXTRA_INTENT, contentSelectionIntent)
        chooserIntent.putExtra(Intent.EXTRA_TITLE, "Image Chooser")
        startActivityForResult(chooserIntent, INPUT_FILE_REQUEST_CODE)
    }

    private fun getRealPathFromURIForGallery(uri: Uri?): String? {
        if (uri == null) {
            return null
        }
        val projection = arrayOf(MediaStore.Images.Media.DATA)
        val cursor: Cursor? = this.contentResolver.query(
            uri, projection, null,
            null, null
        )
        if (cursor != null) {
            val column_index: Int = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DATA)
            cursor.moveToFirst()
            return cursor.getString(column_index)
        }
        assert(false)
        cursor?.close()
        return uri.path
    }

    fun hasStoragePermission(): Boolean {
        var isGranted = true
        if (Build.VERSION.SDK_INT < 23) {
            return true
        }
        val appContext: Context = applicationContext
            ?: return false
        if (appContext.checkSelfPermission("android.permission.WRITE_EXTERNAL_STORAGE") != PackageManager.PERMISSION_GRANTED) {
            isGranted = false
        }
        return isGranted
    }

    override fun openContactBook() {

    }

    override fun openCustomTab(url: String) {
        // implement in app webviewactivity side
    }

    override fun openBookUpdate(bookId: String) {
        // implement in app webviewactivity side
    }

    override fun openBookUpdateWithUseCase(bookId: String, useCase: String) {
        // implement in app webviewactivity side
    }

    override fun trackEvent(
        eventName: String,
        jsonProp: String?,
        amplitude: Boolean,
        cleverTap: Boolean,
        firebase: Boolean,
        appsFlyer: Boolean,
        tiktokEventName: String?,
    ) {

    }

    override fun fetchLocationAndImage(imageType: String) {
        // implement in app webviewactivity side
    }

    override fun fetchAddress() {
        // implement in app webviewactivity side
    }

    override fun getPhoneNumber(): String? {
        // implement in app webviewactivity side
        return null
    }

    override fun action() {
        webView?.loadUrl("javascript:onRetry()")
    }

    override fun redirectToDedicatedLoanBook() {
        // implement in app webviewactivity side
    }

    @SuppressLint("SourceLockedOrientationActivity")
    override fun lockRotation(orientation: String) {
        when {
            orientation.equals(Constant.PORTRAIT, true) -> {
                requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_SENSOR_PORTRAIT
            }
            orientation.equals(Constant.LANDSCAPE, true) -> {
                requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_SENSOR_LANDSCAPE
            }
        }
    }

    override fun timerFinished() {
        handleBackPress()
    }

    override fun trackEvent(eventName: String, eventProp: String) {
        // Implemented in WebviewActivity
    }

    override fun trackUserProperty(propName: String, propValue: String) {
        // Implemented in WebviewActivity
    }

    override fun getBWAppToken(): String? {
        // Implemented in WebviewActivity
        return null
    }

    override fun getBWUserId(): String? {
        // Implemented in WebviewActivity
        return null
    }

    override fun getAppVersionCode(): String? {
        // Implemented in WebviewActivity
        return null
    }

    override fun getBWAppVersionName(): String? {
        // Implemented in WebviewActivity
        return null
    }

    override fun isSaldoActivated(): String? {
        // Implemented in WebviewActivity
        return null
    }

    override fun getBWEntryPoint(): String? {
        // Implemented in WebviewActivity
        return null
    }

    override fun openBWActivity(activity: String, parameter: String, title: String) {
        // Implemented in WebviewActivity
    }

    override fun openWhatsappWithMessage(phoneNumber: String, message: String) {
        // Implemented in WebviewActivity
    }

    override fun copyToClipboard(text: String, toastText: String) {
        // Implemented in WebviewActivity
    }

    override fun shareCalled() {
        // Implemented in WebviewActivity
    }

    override fun getAppealBankAccount() {
        // Implemented in WebviewActivity
    }

    override fun fetchRemoteConfig(key: String): String? {
        // Implemented in WebViewActivity
        return null
    }

    override fun openHelpDialog() {
        // Implemented in WebViewActivity
    }

    override fun openBottomSheet(screenName: String, bottomSheetId: Int) {
        // Implemented in WebViewActivity
    }

    override fun shareWithOpenTray(message: String?, phoneNumber: String?) {
        // Implemented in WebViewActivity
    }

    override fun startOtpVerification(phoneNumber: String, countryCode: String, useCase: String) {
        // Implemented in WebViewActivity
    }

    override fun handleBottomSheetClick(useCase: LocationNotDetectedBottomSheet.UseCase) {
        when (useCase) {
            LocationNotDetectedBottomSheet.UseCase.LOCATION_NOT_DETECTED -> {
                if (ContextCompat.checkSelfPermission(this, locationPermission) != PackageManager.PERMISSION_GRANTED) {
                    if (!ActivityCompat.shouldShowRequestPermissionRationale(this,
                                    Manifest.permission.ACCESS_FINE_LOCATION)) {
                        hasOpenedSettingsForLocationPermission = true
                        startActivity(
                                Intent(
                                        Settings.ACTION_APPLICATION_DETAILS_SETTINGS,
                                        Uri.fromParts("package", this.packageName, null),
                                ),
                        )
                    } else {
                        ActivityCompat.requestPermissions(this, arrayOf(locationPermission), REQUEST_CODE_LOCATION)
                    }
                } else if (!isLocationEnabled()) {
                    startLocationCallback()
                }
            }
            LocationNotDetectedBottomSheet.UseCase.LOCATION_OUT_OF_RANGE -> finish()
        }
    }

    override fun fetchDeviceDetails(): String? {
        return null
    }
    override fun shareOnWhatsapp(phoneNumber: String, message: String, url: String) {
        // Implemented in WebViewActivity
    }

    override fun openWebview(url: String?) {
        // Implemented in WebViewActivity
    }

    override fun openDialog(
        title: String,
        message: String,
        positiveButtonType: String,
        negativeButtonType: String,
        activityUrl: String,
        parameter: String
    ) {
        // Implemented in WebViewActivity
        /*
        ButtonTypes are = "BACK","CUSTOMER_SERVICE","OPEN_ACTIVITY"
         */
    }

}
