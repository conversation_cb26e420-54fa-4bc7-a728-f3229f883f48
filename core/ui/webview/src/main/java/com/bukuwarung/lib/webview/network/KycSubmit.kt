package com.bukuwarung.lib.webview.network

import com.google.gson.annotations.SerializedName

data class KycSubmit (

    @SerializedName("mandatoryDocumentIds")
    var listMandatoryDoc: ArrayList<String>? = null,

    @SerializedName("additionalDocumentIds")
    val listAdditionalDoc: List<String>? = null,

) {
    fun addMandatoryDocument(videoUploadId: String) {
        listMandatoryDoc?.add(videoUploadId)
    }
}