package com.bukuwarung.lib.webview.data

import androidx.annotation.Keep
import com.bukuwarung.lib.webview.network.KYCProvider
import com.google.gson.annotations.SerializedName

@Keep
data class ImageUploads(
    var file1: String,
    var file2: String?,
    @SerializedName("liveliness_details")
    var livelinessDetails: LivelinessDetails?,
    var provider: KYCProvider
)

@Keep
data class LivelinessDetails(
    var result: <PERSON><PERSON><PERSON>,
    @SerializedName("reference_id")
    var referenceId: String,
)
