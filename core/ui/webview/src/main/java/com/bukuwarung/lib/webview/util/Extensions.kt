package com.bukuwarung.lib.webview.util

import android.Manifest
import android.app.Activity
import android.content.IntentSender
import android.content.pm.PackageManager
import android.graphics.ImageDecoder
import android.location.Location
import android.location.LocationManager
import android.os.Build
import android.provider.MediaStore
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.core.location.LocationManagerCompat
import com.google.android.gms.common.api.ResolvableApiException
import com.google.android.gms.location.LocationRequest
import com.google.android.gms.location.LocationServices
import com.google.android.gms.location.LocationSettingsRequest
import com.google.android.gms.location.Priority
import com.google.android.gms.tasks.CancellationTokenSource

internal fun View.hideView() {
    this.visibility = View.GONE
}

internal fun View.showView() {
    this.visibility = View.VISIBLE
}

internal fun View.invisibleView() {
    this.visibility = View.INVISIBLE
}

internal fun Boolean.asVisibility(): Int {
    return if (this) {
        View.VISIBLE
    } else {
        View.GONE
    }
}

internal val Boolean?.isTrue
    get() = (this == true)

internal val Boolean?.toAnalyticsSuccessOrFailure
    get() = if (this == true) AnalyticsUtil.SUCCESS else AnalyticsUtil.FAILED

internal val Boolean?.toAnalyticsYesOrNo
    get() = if (this == true) AnalyticsUtil.YES else AnalyticsUtil.NO

/**
 * Currently it is assumed that fine location permission is required.
 * In case a use case arises in future to ask for course location/background location,
 * permission can be converted into an parameter.
 */
internal fun Activity.getLocationPermission(
    permissionGrantedCallback: () -> Unit,
    requestCode: Int
) {
    if (ActivityCompat.checkSelfPermission(this, Manifest.permission.ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED) {
        permissionGrantedCallback.invoke()
    } else {
        ActivityCompat.requestPermissions(this, arrayOf(Manifest.permission.ACCESS_FINE_LOCATION), requestCode)
    }
}

/**
 * Checks if location request parameters can be supported by the location services,
 * This also checks if GPS is enabled on the device.
 * Call locationServiceSuccessCallback if location settings are successfully accepted by the service.
 */
internal fun Activity.checkLocationService(
    locationServiceSuccessCallback: () -> Unit,
    gpsRequestCode: Int
) {
    val locationRequest: LocationRequest = LocationRequest.create().apply {
        interval = Constant.DEFAULT_INTERVAL
        fastestInterval = Constant.DEFAULT_FASTEST_INTERVAL
        priority = Constant.DEFAULT_PRIORITY
        maxWaitTime = Constant.DEFAULT_MAX_WAIT_TIME
    }
    val builder = LocationSettingsRequest.Builder().addLocationRequest(locationRequest)
    LocationServices.getSettingsClient(this)
            .checkLocationSettings(builder.build())
            .addOnSuccessListener(this) { locationServiceSuccessCallback.invoke() }
            .addOnFailureListener(this) { ex: Exception? ->
                if (ex is ResolvableApiException) {
                    try {
                        ex.startResolutionForResult(this, gpsRequestCode)
                    } catch (sendEx: IntentSender.SendIntentException) {
                        Utils.logException(this, sendEx)
                    }
                }
            }
}

/**
 * Invokes locationSuccessCallback with the location provided by the FusedLocationProvider.
 */
internal fun Activity.getCurrentFusedLocation(
    cancellationTokenSource: CancellationTokenSource,
    locationSuccessCallback: (location: Location) -> Unit
) {
    if (ContextCompat.checkSelfPermission(this, Manifest.permission.ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED) {
        val fusedLocationProviderClient = LocationServices.getFusedLocationProviderClient(applicationContext)
        fusedLocationProviderClient.getCurrentLocation(
                Priority.PRIORITY_HIGH_ACCURACY,
                cancellationTokenSource.token
        ).addOnSuccessListener {
            it?.let { locationSuccessCallback(it) }
        }
    }
}


/**
 * Checks if GPS is enabled on the device.
 */
internal fun Activity.isLocationEnabled(): Boolean {
    val manager: LocationManager = getSystemService(AppCompatActivity.LOCATION_SERVICE) as LocationManager
    return LocationManagerCompat.isLocationEnabled(manager)
}