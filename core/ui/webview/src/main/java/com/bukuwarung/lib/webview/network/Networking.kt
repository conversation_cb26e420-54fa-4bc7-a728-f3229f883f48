package com.bukuwarung.lib.webview.network

import android.util.Log
import com.bukuwarung.lib.webview.BuildConfig
import com.bukuwarung.lib.webview.network.session.SessionRemoteDataSource
import com.google.gson.GsonBuilder
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.util.concurrent.TimeUnit
import com.moczul.ok2curl.CurlInterceptor
import com.moczul.ok2curl.logger.Logger

class Networking {

    private var BASE_URL:String = ""

    fun setBaseUrl(baseUrl:String){
        BASE_URL = baseUrl
    }

    fun provideRetrofit():Retrofit{
        return Retrofit.Builder().baseUrl(BASE_URL)
            .addConverterFactory(GsonConverterFactory.create(provideGson()))
            .client(provideOkHttpClient())
            .build()
    }

    fun provideOkHttpClient(): OkHttpClient {
        val builder: OkHttpClient.Builder = okhttpClientBuilder()
        return builder.build()
    }

    private fun provideGson() = GsonBuilder().setLenient().create()

    private fun okhttpClientBuilder(): OkHttpClient.Builder {
        return OkHttpClient.Builder()
            .addInterceptor(provideLoggingInterceptor())
            .addInterceptor(CurlInterceptor(object : Logger {
                override fun log(message: String) {
                    Log.d("Ok2Curl", message)
                }
            }))
            .callTimeout(2, TimeUnit.MINUTES)
            .connectTimeout(2, TimeUnit.MINUTES)
            .readTimeout(2, TimeUnit.MINUTES)
    }

    private fun provideLoggingInterceptor(): HttpLoggingInterceptor {
        return HttpLoggingInterceptor().apply {
            level =  if (BuildConfig.DEBUG) HttpLoggingInterceptor.Level.BODY else HttpLoggingInterceptor.Level.NONE
        }
    }


    fun provideKycRemoteDataSource():KycRemoteDataSource{
        return provideRetrofit().create(KycRemoteDataSource::class.java)
    }

    fun provideSessionRemoteDataSource(): SessionRemoteDataSource {
        return provideRetrofit().create(SessionRemoteDataSource::class.java)
    }

}