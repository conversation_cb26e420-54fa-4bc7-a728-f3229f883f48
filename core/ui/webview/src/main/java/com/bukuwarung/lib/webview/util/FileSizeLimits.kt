package com.bukuwarung.lib.webview.util

import android.os.Parcelable
import androidx.annotation.Keep
import kotlinx.parcelize.Parcelize

@Keep
data class FileSizeLimits(
    val KTP: Long = 2 * 1024 * 1024,
    val SELFIE: Long = 5 * 1024 * 1024,
    val VIDEO: Long = 10 * 1024 * 1024,
    val MH_TICKET_IMAGE: Long = 900 * 1024,
    val MH_TICKET_PDF: Long = 2 * 1024 * 1024,
    val OTHERS: Long = 1536 * 1024,
)

@Keep
data class UploadsValidations(
    val KTP: UploadsValidation? = null,
    val SELFIE: UploadsValidation? = null,
    val VIDEO: UploadsValidation? = null,
    val MH_TICKET_IMAGE: UploadsValidation? = null,
    val MH_TICKET_PDF: UploadsValidation? = null,
    val OTHERS: UploadsValidation? = null,
)

@Keep
@Parcelize
data class UploadsValidation(
    val maxFileSize: Long? = null,
    val minFileSize: Long? = null,
    val maxImageResolution: Resolution? = null,
    val minImageResolution: Resolution? = null,
    val recommendedResolutionForOS: HashMap<Int, Resolution>? = null
) : Parcelable

@Keep
@Parcelize
data class Resolution(
    val width: Int,
    val height: Int
) : Parcelable

@Keep
@Parcelize
data class DeviceDetails(
    val tid: String? = null,
    val sn: String? = null
) : Parcelable