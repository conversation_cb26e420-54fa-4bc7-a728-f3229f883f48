package com.bukuwarung.lib.webview.kyc

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.bukuwarung.lib.webview.data.ProgressRequestBody
import com.bukuwarung.lib.webview.network.KycRepository
import com.bukuwarung.lib.webview.network.KycSubmit
import com.bukuwarung.lib.webview.network.SessionUseCase
import com.google.gson.Gson
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import okhttp3.MultipartBody
import retrofit2.HttpException
import java.io.InterruptedIOException
import java.net.ConnectException
import java.net.UnknownHostException

class VideoKycPreviewViewModel : ViewModel() {

    private val fileUploadDetail = MutableLiveData<NetworkEvent>()
    val observeDetail: LiveData<NetworkEvent> = fileUploadDetail
    private lateinit var appBaseUrl: String
    private var refreshTokenRetry = 0

    sealed class NetworkEvent {
        data class ApiError(val errorMessage: String) : NetworkEvent()
        object NetworkError : NetworkEvent()
        data class FileUploadSuccess(val documentId: String) : NetworkEvent()
        object KycSuccess : NetworkEvent()
        data class Loading(val isLoading: Boolean) : NetworkEvent()

    }

    fun uploadVideo(
        requestBody: ProgressRequestBody, authToken: String?, fileName: String,
        authUrl: String, sessionToken: String
    ) {
        showLoader(true)

        val repository = KycRepository.getInstance(appBaseUrl)
        val auth = "Bearer $authToken"

        val filePart: MultipartBody.Part =
            MultipartBody.Part.createFormData("file", fileName, requestBody)

        GlobalScope.launch(Dispatchers.Main) {
            launch(Dispatchers.IO) {
                try {
                    val response = repository.uploadKycVideoWithAuth(auth, filePart)
                    withContext(Dispatchers.Main) {
                        response.attachmentId?.let {
                            updateStatus(it)
                        } ?: updateErrorStatus()
                    }
                } catch (ex: Exception) {
                    /*
                        Added refreshTokenRetry to break recursion just in case auth apis sends
                        invalid tokens.
                    */
                    if ((ex as? HttpException)?.code() == 401 && refreshTokenRetry < 2) {
                        try {
                            refreshTokenRetry++
                            val newSession =
                                SessionUseCase.createNewSession(authUrl, sessionToken)
                            uploadVideo(
                                requestBody, newSession.idToken, fileName,
                                authUrl, newSession.refreshToken
                            )
                        } catch (ex: Exception) {
                            handleException(ex)
                        }
                    } else {
                        handleException(ex)
                    }
                }
            }
        }
    }

    private fun updateErrorStatus() {
        fileUploadDetail.postValue(NetworkEvent.ApiError("Unable to upload"))
    }

    private fun updateStatus(documentId: String) {
        fileUploadDetail.postValue(NetworkEvent.FileUploadSuccess(documentId))
    }

    fun finishKyc(token: String?, ids: String?, videoUploadId: String) {
        val repository = KycRepository.getInstance(baseUrl = appBaseUrl)
        val auth = "Bearer $token"
        val kycRequest = Gson().fromJson(ids, KycSubmit::class.java)
        kycRequest.addMandatoryDocument(videoUploadId)
        GlobalScope.launch(Dispatchers.IO) {
            try {
                val result = repository.submitKyc(auth, kycRequest)
                withContext(Dispatchers.Main) {
                    if (result.code == 200)
                        fileUploadDetail.postValue(NetworkEvent.KycSuccess)
                    else {
                        updateErrorStatus()
                    }
                }
            } catch (ex: Exception) {
                handleException(ex)
            }
        }
    }

    private fun handleException(ex: Exception) {
        GlobalScope.launch(Dispatchers.Main) {
            fileUploadDetail.value =  when (ex) {
                is UnknownHostException, is ConnectException, is InterruptedIOException -> NetworkEvent.NetworkError
                else -> NetworkEvent.ApiError(ex.localizedMessage.orEmpty())
            }

            showLoader(false)
        }
    }

    fun showLoader(isLoading: Boolean) {
        GlobalScope.launch(Dispatchers.Main) {
            fileUploadDetail.postValue(NetworkEvent.Loading(isLoading))
        }
    }

    fun setBaseUrl(baseUrl: String?) {
        baseUrl?.let {
            this.appBaseUrl = baseUrl
        }

    }

}