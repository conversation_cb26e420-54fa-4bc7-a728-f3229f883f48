package com.bukuwarung.lib.webview.geocoding

import android.app.Application
import android.location.Geocoder
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.google.gson.Gson
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.lang.Exception
import java.util.*

class GeocodingViewModel(application: Application) : AndroidViewModel(application) {

    private val eventStatus = MutableLiveData<Event>()
    val observeEvent: LiveData<Event> = eventStatus

    sealed class Event {
        data class AddressFound(val address: String) : Event()
        data class AddressError(val exception: Exception) : Event()
    }

    private suspend fun setEventStatus(event: Event) = withContext(Dispatchers.Main) {
        eventStatus.value = event
    }

    fun geocodeAddress(latitude: Double, longitude: Double) = viewModelScope.launch {
        withContext(Dispatchers.IO) {
            try {
                val currentLocation = Geocoder(getApplication<Application>().applicationContext, Locale.getDefault())
                    .getFromLocation(latitude, longitude, 1)?.get(0)
                val address = Address(
                    id = "",
                    name = "",
                    fullAddress = currentLocation?.getAddressLine(0),
                    latitude = currentLocation?.latitude,
                    longitude = currentLocation?.longitude,
                    province = currentLocation?.adminArea ?: "",
                    city = currentLocation?.subAdminArea ?: "",
                    district = currentLocation?.locality ?: "",
                    subDistrict = currentLocation?.subLocality ?: "",
                    postalCode = currentLocation?.postalCode ?: ""
                )
                setEventStatus(Event.AddressFound(Gson().toJson(address)))
            } catch (exception: Exception) {
                setEventStatus(Event.AddressError(exception))
            }
        }
    }

}