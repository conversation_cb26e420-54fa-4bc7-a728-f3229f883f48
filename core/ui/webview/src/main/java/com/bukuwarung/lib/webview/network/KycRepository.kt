package com.bukuwarung.lib.webview.network

import com.bukuwarung.lib.webview.data.ImageUploads
import okhttp3.MultipartBody
import retrofit2.http.Query

class KycRepository private constructor() {

    companion object {
        private val instance = KycRepository()
        private val network = Networking()
        lateinit var kycRemoteDataSource: KycRemoteDataSource

        fun getInstance(baseUrl: String): KycRepository {
            network.setBaseUrl(baseUrl)
            kycRemoteDataSource = network.provideKycRemoteDataSource()
            return instance
        }
    }

    suspend fun uploadKycVideoWithAuth(auth: String, file: MultipartBody.Part) =
        kycRemoteDataSource.uploadKycVideo(auth, file)

    suspend fun uploadKycImagesWithAuth(auth: String, imageUploads: ImageUploads) =
        kycRemoteDataSource.uploadKycImages(auth, imageUploads)

    suspend fun uploadKycSelfieMultipartWithAuth(
        auth: String,
        provider: KYCProvider,
        kycFlow: String?,
        reKycType: String?,
        file: MultipartBody.Part,
        bukuOrigin: String
    ) = kycRemoteDataSource.uploadKycSelfieMultipart(bukuOrigin, auth, provider, kycFlow, reKycType, file)

    suspend fun submitKyc(auth: String, kycData: KycSubmit) =
        kycRemoteDataSource.submitKyc(auth, kycData)

    suspend fun getActiveProviders(auth: String, bukuOrigin: String) = kycRemoteDataSource.getActiveProviders(auth, bukuOrigin)
}