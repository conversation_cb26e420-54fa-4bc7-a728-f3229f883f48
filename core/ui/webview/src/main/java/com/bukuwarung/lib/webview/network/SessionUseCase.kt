package com.bukuwarung.lib.webview.network

import com.bukuwarung.lib.webview.network.session.NewSessionRequest
import com.bukuwarung.lib.webview.network.session.NewSessionResponse
import com.bukuwarung.lib.webview.network.session.SessionRepository
import retrofit2.HttpException

object SessionUseCase {

    @Throws(HttpException::class)
    suspend fun createNewSession(authUrl: String, sessionToken: String): NewSessionResponse {
        val repository = SessionRepository.getInstance(authUrl)
        val newSessionRequest = NewSessionRequest(sessionToken)
        return repository.createNewSession(newSessionRequest)
    }
}