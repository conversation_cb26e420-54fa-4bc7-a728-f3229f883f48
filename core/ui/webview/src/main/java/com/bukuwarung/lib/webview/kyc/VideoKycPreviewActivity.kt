package com.bukuwarung.lib.webview.kyc

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.TextPaint
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.view.View
import android.widget.MediaController
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.lifecycle.ViewModelProvider
import com.bukuwarung.lib.webview.R
import com.bukuwarung.lib.webview.SimpleWebViewActivity
import com.bukuwarung.lib.webview.bottomsheet.ErrorBottomSheet
import com.bukuwarung.lib.webview.camera.VideoKycActivity
import com.bukuwarung.lib.webview.data.ProgressRequestBody
import com.bukuwarung.lib.webview.databinding.ActivityPreviewKycVideoBinding
import com.bukuwarung.lib.webview.dialog.CompressionDialog
import com.bukuwarung.lib.webview.util.AnalyticsUtil
import com.bukuwarung.lib.webview.util.Constant.DATA_PRIVACY_LINK
import com.bukuwarung.lib.webview.util.Constant.HELP_LINK
import com.bukuwarung.lib.webview.util.Utils
import java.io.File

class VideoKycPreviewActivity : AppCompatActivity() {

    companion object {
        const val FILE_PATH = "filePath"
        private const val AUTH_URL = "auth_url"
        private const val SESSION_TOKEN = "session_token"

        fun createIntent(
            origin: Context?,
            path: String,
            inputIds: String?,
            isFromQris: Boolean?,
            token: String?,
            baseUrl: String?,
            authUrl: String?, sessionToken: String?
        ): Intent {
            val intent = Intent(origin, VideoKycPreviewActivity::class.java)
            intent.apply {
                putExtra(FILE_PATH, path)
                putExtra(VideoKycActivity.DOCUMENT_ID, inputIds)
                putExtra(VideoKycActivity.TOKEN, token)
                putExtra(VideoKycActivity.FROM_QRIS, isFromQris)
                putExtra(VideoKycActivity.BASE_URL, baseUrl)
                putExtra(AUTH_URL, authUrl)
                putExtra(SESSION_TOKEN, sessionToken)
            }
            return intent
        }
    }


    lateinit var binder: ActivityPreviewKycVideoBinding
    var dialog: CompressionDialog? = null
    private val videoPath by lazy {
        intent?.getStringExtra(FILE_PATH)
    }

    private val baseUrl by lazy {
        intent?.getStringExtra(VideoKycActivity.BASE_URL)
    }

    private val isFromQris by lazy {
        intent?.getStringExtra(VideoKycActivity.FROM_QRIS)
    }


    private val inputIds by lazy {
        intent?.getStringExtra(VideoKycActivity.DOCUMENT_ID)
    }
    private val token by lazy {
        intent?.getStringExtra(VideoKycActivity.TOKEN)
    }
    private val authUrl by lazy { intent?.getStringExtra(AUTH_URL) }
    private val sessionToken by lazy { intent?.getStringExtra(SESSION_TOKEN) }
    private lateinit var viewModel: VideoKycPreviewViewModel

    private var mediaController: MediaController? = null
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binder = ActivityPreviewKycVideoBinding.inflate(layoutInflater)
        setContentView(binder.root)
        viewModel = ViewModelProvider(this).get(VideoKycPreviewViewModel::class.java)
        viewModel.setBaseUrl(baseUrl)

        setupVideoView()
        setupViews()
        subscribeState()
    }

    private fun subscribeState() {
        viewModel.observeDetail.observe(this) {
            when (it) {
                is VideoKycPreviewViewModel.NetworkEvent.ApiError -> {
                    showDialog(false)
                    if (it.errorMessage.isNotEmpty()) {
                        binder.tvError.text = it.errorMessage
                    } else {
                        binder.tvError.text = getString(R.string.video_upload_error)
                    }
                    binder.tvError.visibility = View.VISIBLE
                    logVideoUploadEvent(AnalyticsUtil.FAILED)
                }

                VideoKycPreviewViewModel.NetworkEvent.NetworkError -> {
                    ErrorBottomSheet.createInstance(
                        false, getString(R.string.lib_no_connection_message),
                        callActionOnDismiss = true, setCancellable = false
                    ).show(supportFragmentManager, ErrorBottomSheet.TAG)
                    logVideoUploadEvent(AnalyticsUtil.FAILED)
                }

                is VideoKycPreviewViewModel.NetworkEvent.Loading -> {
                    showDialog(it.isLoading)
                }

                is VideoKycPreviewViewModel.NetworkEvent.FileUploadSuccess -> {
                    finalizeKyc(it.documentId)
                    dialog?.uploadSuccess()
                    logVideoUploadEvent(AnalyticsUtil.SUCCESS)
                }

                is VideoKycPreviewViewModel.NetworkEvent.KycSuccess -> {
                    setResult(retake = false)
                }
            }
        }
    }

    private fun logVideoUploadEvent(status: String) {
        val props = mutableMapOf<String?, Any?>()
        props[AnalyticsUtil.STATUS] = status
        AnalyticsUtil.logEvent(
            this,
            AnalyticsUtil.EVENT_KYC_VIDEO_SUBMIT_CLICKED,
            AnalyticsUtil.getJsonStringOfMap(props)
        )
    }

    private fun showDialog(show: Boolean) {
        runOnUiThread {
            if (show) {
                dialog = CompressionDialog(this@VideoKycPreviewActivity)
                dialog?.show()
                dialog?.setTitleText(getString(R.string.upload_video))
            } else {
                dialog?.dismiss()
            }
        }
    }

    private fun setupViews() {
        with(binder) {
            includeToolbar.tvTitle.text = getString(R.string.title_video_preview)
            ivPlay.setOnClickListener {
                it.visibility = View.GONE
                binder.videoView.start()
                Handler(Looper.getMainLooper()).postDelayed({
                    mediaController?.show(0)
                }, 200)

            }

            btnRetake.setOnClickListener {
                setResult(retake = true)
            }

            btnSend.setOnClickListener {
                videoPath?.let { path ->
                    val file = File(path)
                    val requestBody = ProgressRequestBody(file, "video/mp4", 1)
                    requestBody.progress.observe(this@VideoKycPreviewActivity) {
                        dialog?.updateProgress(it.toInt())
                    }
                    viewModel.uploadVideo(
                        requestBody, token, file.name, authUrl.orEmpty(), sessionToken.orEmpty()
                    )
                }
            }

            includeToolbar.btnBack.setOnClickListener {
                setResult(retake = true)
            }
            includeToolbar.tvHelp.setOnClickListener {
                startActivity(
                    SimpleWebViewActivity.createIntent(
                        this@VideoKycPreviewActivity,
                        HELP_LINK,
                        getString(
                            R.string.lib_help_bantuan
                        )
                    )
                )
            }

            binder.tvSecure2.text =
                Utils.makeSectionOfTextClickable(
                    getString(R.string.lib_data_security),
                    getString(R.string.lib_cliable_data_security),
                    object : ClickableSpan() {
                        override fun onClick(widget: View) {
                            Utils.launchBrowser(
                                this@VideoKycPreviewActivity,
                                DATA_PRIVACY_LINK
                            )
                        }

                        override fun updateDrawState(ds: TextPaint) {
                            super.updateDrawState(ds)
                            ds.isUnderlineText = false
                            ds.color = ContextCompat.getColor(
                                this@VideoKycPreviewActivity,
                                R.color.colorPrimary
                            )
                        }

                    },
                    false
                )

            binder.tvSecure2.movementMethod = LinkMovementMethod.getInstance()
            binder.tvSecure2.highlightColor = Color.TRANSPARENT
        }

    }

    private fun setResult(retake: Boolean) {
        val intent = Intent()
        intent.apply {
            if (retake) {
                putExtra(VideoKycActivity.ACTION, true)
            }
            putExtra(VideoKycActivity.FROM_QRIS, isFromQris)
        }


        setResult(Activity.RESULT_OK, intent)
        finish()
    }


    private fun setupVideoView() {
        with(binder) {
            videoView.setVideoPath(videoPath)
            mediaController = MediaController(this@VideoKycPreviewActivity)
            videoView.setMediaController(mediaController)
            videoView.requestFocus()
            videoView.seekTo(2)
            videoView.setOnCompletionListener {
                ivPlay.visibility = View.VISIBLE
            }
        }
    }

    private fun finalizeKyc(videoUploadId: String) {
        viewModel.finishKyc(token, inputIds, videoUploadId)
    }

    override fun onBackPressed() {
        setResult(true)
    }
}