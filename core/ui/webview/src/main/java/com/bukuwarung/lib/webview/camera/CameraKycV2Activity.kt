package com.bukuwarung.lib.webview.camera

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Build
import android.os.Bundle
import android.view.View
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.camera.core.*
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.core.content.ContextCompat
import androidx.lifecycle.ViewModelProvider
import com.bukuwarung.lib.webview.BaseWebviewActivity
import com.bukuwarung.lib.webview.R
import com.bukuwarung.lib.webview.SimpleWebViewActivity
import com.bukuwarung.lib.webview.bottomsheet.ErrorBottomSheet
import com.bukuwarung.lib.webview.bottomsheet.SizeLimitErrorBS
import com.bukuwarung.lib.webview.databinding.ActivityCameraKycV2Binding
import com.bukuwarung.lib.webview.dialog.GenericDialog
import com.bukuwarung.lib.webview.network.KYCProvider
import com.bukuwarung.lib.webview.util.*
import com.bukuwarung.lib.webview.util.Constant.HELP_LINK
import com.bukuwarung.lib.webview.util.Constant.MEGA_BYTE_1_5
import com.bumptech.glide.Glide
import com.google.gson.Gson
import com.google.gson.JsonObject
import java.io.File
import java.util.*
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors


class CameraKycV2Activity : AppCompatActivity(),
    SizeLimitErrorBS.Callback, ErrorBottomSheet.Callback {

    companion object {
        private const val REQUEST_CODE_PERMISSIONS = 10
        private const val USE_CASE = "use_case"
        private const val AUTH_TOKEN = "auth_token"
        private const val BASE_URL = "base_url"
        private const val CHECK_LUMINOSITY = "check_luminosity"
        private const val MIN_LUMINOSITY = "min_luminosity"
        private const val COMPRESSION_SIZE = "compression_size"
        private const val AUTH_URL = "auth_url"
        private const val SESSION_TOKEN = "session_token"
        private const val FILE_SIZE_LIMIT = "file_size_limit"
        private const val IMAGE_QUALITY = "image_quality"
        private const val UPLOAD_VALIDATION = "upload_validation"
        private const val KYC_PROVIDER = "kyc_provider"
        private const val RESOLUTION_WIDTH = "resolution_width"
        private const val RESOLUTION_HEIGHT = "resolution_height"
        private const val PERFORM_VALIDATIONS = "perform_validations"
        private const val EVENT_PROPS = "event_props"
        private const val KYC_FLOW = "kyc_flow"
        private const val RE_KYC_TYPE = "re_kyc_type"
        private const val IS_MINIATM = "is_miniatm"
        private val REQUIRED_PERMISSIONS = arrayOf(Manifest.permission.CAMERA)

        fun createIntent(
            origin: Context, type: String, token: String?, baseUrl: String?,
            checkLuminosity: Boolean = false, minLuminosity: Float = 0f,
            compressionSize: Float = (FileSizeLimits().OTHERS / 1024f),
            authUrl: String?, sessionToken: String?, sizeLimit: Long, imageQuality: Int,
            uploadValidation: UploadsValidation? = null,
            performValidations: Boolean = false,
            resolutionWidth: Int? = null, resolutionHeight: Int? = null,
            eventProps: String? = "", kycProvider: KYCProvider,
            kycFlow: String?,
            reKycType: String?,
            isMiniAtm: Boolean = false
        ): Intent {
            val intent = Intent(origin, CameraKycV2Activity::class.java)
            intent.putExtra(USE_CASE, type)
            intent.putExtra(AUTH_TOKEN, token)
            intent.putExtra(BASE_URL, baseUrl)
            intent.putExtra(CHECK_LUMINOSITY, checkLuminosity)
            intent.putExtra(MIN_LUMINOSITY, minLuminosity)
            intent.putExtra(COMPRESSION_SIZE, compressionSize)
            intent.putExtra(AUTH_URL, authUrl)
            intent.putExtra(SESSION_TOKEN, sessionToken)
            intent.putExtra(FILE_SIZE_LIMIT, sizeLimit)
            intent.putExtra(IMAGE_QUALITY, imageQuality)
            intent.putExtra(UPLOAD_VALIDATION, uploadValidation)
            intent.putExtra(RESOLUTION_WIDTH, resolutionWidth)
            intent.putExtra(RESOLUTION_HEIGHT, resolutionHeight)
            intent.putExtra(PERFORM_VALIDATIONS, performValidations)
            intent.putExtra(EVENT_PROPS, eventProps)
            intent.putExtra(KYC_PROVIDER, kycProvider)
            intent.putExtra(KYC_FLOW, kycFlow)
            intent.putExtra(RE_KYC_TYPE, reKycType)
            intent.putExtra(IS_MINIATM, isMiniAtm)
            return intent
        }
    }

    private var imageCapture: ImageCapture? = null
    private lateinit var outputDirectory: File
    private var cameraExecutor: ExecutorService? = null
    private var lensFacing = CameraSelector.LENS_FACING_FRONT
    private lateinit var cameraProvider: ProcessCameraProvider
    private var camera: Camera? = null
    private var flashType = ImageCapture.FLASH_MODE_OFF
    private val LIVELINESS_DIR = "liveliness"
    private lateinit var viewModel: CameraKycV2ViewModel
    private lateinit var binding: ActivityCameraKycV2Binding
    private val useCase by lazy { intent?.getStringExtra(USE_CASE) ?: Constant.TYPE_BASIC_KYC }
    private val token by lazy { intent?.getStringExtra(AUTH_TOKEN) }
    private val baseUrl by lazy { intent?.getStringExtra(BASE_URL) }
    private val authUrl by lazy { intent?.getStringExtra(AUTH_URL) }
    private val sessionToken by lazy { intent?.getStringExtra(SESSION_TOKEN) }
    private val checkLuminosity by lazy { intent?.getBooleanExtra(CHECK_LUMINOSITY, false) }
    private val minLuminosity by lazy { intent?.getFloatExtra(MIN_LUMINOSITY, 0f) }
    private val compressionSize by lazy {
        intent?.getFloatExtra(COMPRESSION_SIZE, MEGA_BYTE_1_5) ?: MEGA_BYTE_1_5
    }
    private val sizeLimit by lazy {
        intent?.getLongExtra(FILE_SIZE_LIMIT, FileSizeLimits().SELFIE) ?: FileSizeLimits().SELFIE
    }
    private val imageQuality by lazy {
        intent?.getIntExtra(IMAGE_QUALITY, Constant.IMAGE_QUALITY_DEFAULT)
            ?: Constant.IMAGE_QUALITY_DEFAULT
    }
    private val uploadValidation by lazy {
        intent?.getParcelableExtra(UPLOAD_VALIDATION) as? UploadsValidation
    }
    private val kycProvider by lazy {
        intent?.getSerializableExtra(KYC_PROVIDER) as? KYCProvider
    }
    private val performValidations by lazy { intent?.getBooleanExtra(PERFORM_VALIDATIONS, false) }
    private val resolutionWidth by lazy { intent?.getIntExtra(RESOLUTION_WIDTH, -1) }
    private val resolutionHeight by lazy { intent?.getIntExtra(RESOLUTION_HEIGHT, -1) }
    private val eventProps by lazy { intent?.getStringExtra(EVENT_PROPS) }
    private val kycFlow by lazy { intent?.getStringExtra(KYC_FLOW) }
    private val reKycType by lazy { intent?.getStringExtra(RE_KYC_TYPE) }
    private val isMiniAtm by lazy { intent?.getBooleanExtra(IS_MINIATM, false).isTrue }
    private var imageAnalysis: ImageAnalysis? = null
    private var photoTaken: Boolean = false
    private var photoFile: File? = null
    private var destFile: File? = null
    private lateinit var luminosityAnalyzer: LuminosityAnalyzer

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        viewModel = ViewModelProvider(this).get(CameraKycV2ViewModel::class.java)
        viewModel.setBaseUrl(baseUrl)

        setViewBinding()
        setupView()

        viewModel.event.observe(this) {
            when (it) {
                is CameraKycV2ViewModel.Event.ApiError -> handleApiError(
                    it.isServerError,
                    it.errorMessage
                )
                CameraKycV2ViewModel.Event.NetworkError ->
                    handleApiError(false, getString(R.string.lib_no_connection_message))
                is CameraKycV2ViewModel.Event.SelfieUploadSuccess -> {
                    logUploadSuccessEvent()
                    setResult(Activity.RESULT_OK)
                    finish()
                }
                is CameraKycV2ViewModel.Event.Loading -> {
                    binding.progressBar.visibility = if (it.isLoading) View.VISIBLE else View.GONE
                }
                CameraKycV2ViewModel.Event.KycProviderMismatch -> {
                    logUploadFailedEvent(AnalyticsUtil.KYC_PROVIDER_MISMATCH)
                    showKycProviderMismatchError()
                }
            }
        }
    }

    private fun showKycProviderMismatchError() {
        GenericDialog.create(context = this) {
            titleRes = R.string.aw_kyc_provider_error_title
            bodyRes = R.string.aw_kyc_provider_error_message
            btnRightRes = R.string.lib_retake_photo
            btnLeftRes = null
            rightBtnCallback = {
                setResult(Activity.RESULT_OK, Intent().apply {
                    putExtra(BaseWebviewActivity.KYC_PROVIDER_MISMATCH, true)
                })
                finish()
            }
        }.show()
    }

    private fun logUploadSuccessEvent() {
        val props = mutableMapOf<String?, Any?>()
        props[AnalyticsUtil.STATUS] = AnalyticsUtil.SUCCESS
        AnalyticsUtil.addExtraEventProps(eventProps, props)
        AnalyticsUtil.logEvent(
            this@CameraKycV2Activity,
            AnalyticsUtil.EVENT_SELFIE_UPLOAD_COMPLETED,
            AnalyticsUtil.getJsonStringOfMap(props)
        )
    }

    private fun logUploadFailedEvent(message: String) {
        val props = mutableMapOf<String?, Any?>()
        props[AnalyticsUtil.STATUS] = AnalyticsUtil.FAILED
        props[AnalyticsUtil.REASON] = message
        AnalyticsUtil.addExtraEventProps(eventProps, props)
        AnalyticsUtil.logEvent(
            this@CameraKycV2Activity,
            AnalyticsUtil.EVENT_SELFIE_UPLOAD_COMPLETED,
            AnalyticsUtil.getJsonStringOfMap(props)
        )
    }

    private fun handleApiError(isServerError: Boolean, errorMessage: String) {
        logUploadFailedEvent(errorMessage)
        ErrorBottomSheet.createInstance(
            isServerError, errorMessage,
            callActionOnDismiss = true, setCancellable = false
        ).show(supportFragmentManager, ErrorBottomSheet.TAG)
    }

    private fun setViewBinding() {
        binding = ActivityCameraKycV2Binding.inflate(layoutInflater)
        setContentView(binding.root)
    }

    private fun setupView() {

        with(binding) {
            includeToolbar.tvTitle.setText(R.string.lib_take_face_photo)

            includeToolbar.btnBack.setOnClickListener {
                onBackPressed()
            }
            if (isMiniAtm) includeToolbar.tvHelp.hideView()
            includeToolbar.tvHelp.text = getString(R.string.lib_help_bantuan)
            includeToolbar.tvHelp.setOnClickListener {
                startActivity(
                    SimpleWebViewActivity.createIntent(
                        this@CameraKycV2Activity, HELP_LINK, getString(
                            R.string.lib_help_bantuan
                        )
                    )
                )
            }

            btnCameraRetake.setOnClickListener {
                resetToCaptureImage()
            }

            btnCameraUpload.setOnClickListener {
                photoFile?.let { file ->
                    tvProcessing.invisibleView()
                    grSelfieConfirmation.hideView()
                    grCameraActions.invisibleView()
                    val fileToUpload = destFile ?: file
                    viewModel.uploadImage(
                        fileToUpload,
                        token,
                        authUrl.orEmpty(),
                        sessionToken.orEmpty(),
                        kycProvider ?: KYCProvider.VIDA,
                        kycFlow,
                        reKycType,
                        isMiniAtm
                    )
                }
            }

            btnCameraCapture.setOnClickListener {
                logSelfieClickEvent()
                takePhoto()
            }

            // Request camera permissions
            if (allPermissionsGranted()) {
                startCamera()
            } else {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                    requestPermissions(REQUIRED_PERMISSIONS, REQUEST_CODE_PERMISSIONS)
                }
            }
        }

        outputDirectory = getOutputDirectory()
        cameraExecutor = Executors.newSingleThreadExecutor()

        luminosityAnalyzer = LuminosityAnalyzer { luma ->
            if (photoTaken) return@LuminosityAnalyzer
            runOnUiThread {
                if (luma < (minLuminosity ?: 0f)) {
                    binding.tvError.visibility = View.VISIBLE
                    binding.btnCameraCapture.isEnabled = false
                } else {
                    binding.tvError.visibility = View.GONE
                    binding.btnCameraCapture.isEnabled = true
                }
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        cameraExecutor?.shutdown()
    }

    override fun onBackPressed() {
        GenericDialog.create(context = this) {
            titleRes = R.string.lib_exit_confirmation_title
            bodyRes = R.string.lib_exit_confirmation_body
            btnLeftRes = R.string.lib_batal
            btnRightRes = R.string.lib_exit_confirm
            rightBtnCallback = {
                super.onBackPressed()
            }
        }.show()
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == REQUEST_CODE_PERMISSIONS) {
            if (grantResults.isNotEmpty()
                && grantResults[0] == PackageManager.PERMISSION_GRANTED
            ) {
                startCamera()
            } else {
                finish()
            }
        }
    }

    private fun logSelfieClickEvent() {
        val nextPage = when (useCase) {
            Constant.TYPE_BASIC_KYC, Constant.TYPE_BASIC_QRIS, Constant.TYPE_KYC_EDC_ORDER_DETAIL -> AnalyticsUtil.SUBMIT_KYC
            Constant.TYPE_KYC_W_DOCS, Constant.TYPE_QRIS_W_DOCS -> AnalyticsUtil.ADD_ADDITIONAL_DOCS
            Constant.TYPE_IS_LENDING -> AnalyticsUtil.LENDING
            Constant.TYPE_IS_BNPL_REGISTRATION_COMMERCE -> AnalyticsUtil.BNPL_REGISTRATION_COMMERCE
            Constant.TYPE_IS_BNPL_REGISTRATION_PPOB -> AnalyticsUtil.BNPL_REGISTRATION_PPOB
            Constant.TYPE_IS_BNPL -> AnalyticsUtil.BNPL
            else -> useCase
        }
        val props = mutableMapOf<String?, Any?>()
        props[AnalyticsUtil.NEXT_PAGE] = nextPage
        AnalyticsUtil.addExtraEventProps(eventProps, props)
        AnalyticsUtil.logEvent(
            this@CameraKycV2Activity,
            AnalyticsUtil.EVENT_SELFIE_CONFIRM_CLICKED,
            AnalyticsUtil.getJsonStringOfMap(props)
        )
    }

    private fun takePhoto() {
        showImageInProgress()
        photoTaken = true
        // Get a stable reference of the modifiable image capture use case
        val imageCapture = imageCapture ?: return
        photoFile = File(outputDirectory, "${LIVELINESS_DIR}_${Date().time}.jpg")

        photoFile?.let { file ->
            // Create output options object which contains file + metadata
            val outputOptions = ImageCapture.OutputFileOptions.Builder(file).build()

            // Set up image capture listener, which is triggered after photo has
            // been taken
            imageCapture.takePicture(
                outputOptions,
                ContextCompat.getMainExecutor(this),
                object : ImageCapture.OnImageSavedCallback {
                    override fun onError(exc: ImageCaptureException) {
                        resetToCaptureImage()
                        Toast.makeText(
                            this@CameraKycV2Activity, exc.localizedMessage, Toast.LENGTH_SHORT
                        ).show()
                    }

                    override fun onImageSaved(output: ImageCapture.OutputFileResults) {
                        with(binding) {
                            Glide.with(this@CameraKycV2Activity).load(file).centerCrop()
                                .into(ivPreview)
                            ivPreview.visibility = View.VISIBLE
                            progressBar.visibility = View.GONE
                        }
                        destFile = try {
                            val recommendedWidth =
                                if (resolutionWidth == -1) null else resolutionWidth
                            val recommendedHeight =
                                if (resolutionHeight == -1) null else resolutionHeight
                            ImageUtils.compressImage(
                                this@CameraKycV2Activity, file, sizeLimit, imageQuality,
                                width = recommendedWidth, height = recommendedHeight
                            )
                        } catch (ex: Exception) {
                            Utils.logException(this@CameraKycV2Activity, ex)
                            // Fallback to old way of compression in case library throws any unknown error
                            ImageUtils.compressImage(file.absolutePath, compressionSize)
                            null
                        }
                        val fileSize = destFile?.length() ?: file.length()
                        if (performValidations == true && hasValidationError(file)) {
                            return
                        }
                        if (fileSize > sizeLimit) {
                            showMaxFileSizeError(sizeLimit)
                        } else {
                            with(binding) {
                                tvSelfieTitle.text = getString(R.string.lib_confirm_selfie)
                                grCameraActions.visibility = View.VISIBLE
                                grSelfieConfirmation.visibility = View.VISIBLE
                                btnCameraCapture.visibility = View.GONE
                            }
                        }
                    }
                })
        }
    }

    private fun hasValidationError(file: File): Boolean {
        val validationError = ImageUtils.performImageValidations(
            this, file, uploadValidation, Constant.TYPE_SELFIE_KTP
        )
        val errorMessage =
            ImageUtils.getValidationErrorMessage(this, validationError, uploadValidation)
        if (errorMessage != null) {
            SizeLimitErrorBS.createInstance(sizeLimit, errorMessage)
                .show(supportFragmentManager, SizeLimitErrorBS.TAG)
            return true
        }
        return false
    }

    private fun showMaxFileSizeError(sizeLimit: Long) {
        binding.progressBar.visibility = View.GONE
        SizeLimitErrorBS.createInstance(sizeLimit)
            .show(supportFragmentManager, SizeLimitErrorBS.TAG)
    }

    private fun showImageInProgress() {
        with(binding) {
            progressBar.visibility = View.VISIBLE
            btnCameraCapture.visibility = View.INVISIBLE
        }
    }

    private fun resetToCaptureImage() {
        photoTaken = false
        photoFile = null
        with(binding) {
            tvSelfieTitle.text = getString(R.string.lib_liveliness_selfie_title)
            progressBar.visibility = View.INVISIBLE
            btnCameraCapture.visibility = View.VISIBLE
            ivPreview.visibility = View.GONE
            grSelfieConfirmation.visibility = View.GONE
            grCameraActions.visibility = View.GONE
        }
        startCamera()
    }

    private fun startCamera() {
        val cameraProviderFuture = ProcessCameraProvider.getInstance(this)

        cameraProviderFuture.addListener({
            // Used to bind the lifecycle of cameras to the lifecycle owner
            cameraProvider = cameraProviderFuture.get()
            val cameraSelector = CameraSelector.Builder().requireLensFacing(lensFacing).build()

            // Preview
            val preview = Preview.Builder()
                .build()
                .also {
                    it.setSurfaceProvider(binding.previewCamera.surfaceProvider)
                }

            imageCapture = ImageCapture.Builder().setFlashMode(flashType).build()

            // Select back camera as a default

            try {
                // Unbind use cases before rebinding
                cameraProvider.unbindAll()
                imageAnalysis = ImageAnalysis.Builder().build()
                if (checkLuminosity == true) {
                    cameraExecutor?.let {
                        imageAnalysis?.setAnalyzer(it, luminosityAnalyzer)
                    }
                }
                // Bind use cases to camera
                camera = cameraProvider.bindToLifecycle(
                    this, cameraSelector, preview, imageCapture, imageAnalysis
                )
                binding.btnCameraCapture.isEnabled = true
            } catch (exc: Exception) {
                Utils.logException(this, exc)
                binding.tvError.apply {
                    showView()
                    text = getString(R.string.aw_front_camera_missing_error)
                }
                binding.btnCameraCapture.isEnabled = false
            }
        }, ContextCompat.getMainExecutor(this))
    }

    private fun allPermissionsGranted() = REQUIRED_PERMISSIONS.all {
        ContextCompat.checkSelfPermission(baseContext, it) == PackageManager.PERMISSION_GRANTED
    }

    private fun getOutputDirectory(): File {
        val mediaDir = externalCacheDirs.firstOrNull()?.let {
            File(it, LIVELINESS_DIR).apply { mkdirs() }
        }
        return if (mediaDir != null && mediaDir.exists()) mediaDir else filesDir
    }

    override fun sizeLimitAction() {
        resetToCaptureImage()
    }

    override fun action() {
        resetToCaptureImage()
    }
}
