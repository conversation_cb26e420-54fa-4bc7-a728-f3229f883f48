package com.bukuwarung.lib.webview.dialog

import android.app.Dialog
import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.Gravity
import android.view.ViewGroup
import com.bukuwarung.lib.webview.R

class VideoAnalyseDialog(context: Context) : Dialog(context) {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.dialog_video_analyse)
        window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        setMinWidth()

    }

    fun setMinWidth() {
        val minWidth = (context.resources.displayMetrics.widthPixels * 0.90).toInt()
        window?.setLayout(minWidth, ViewGroup.LayoutParams.WRAP_CONTENT);
        window?.setGravity(Gravity.CENTER);
    }
}