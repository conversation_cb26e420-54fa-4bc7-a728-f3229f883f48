package com.bukuwarung.lib.webview.dialog

import android.annotation.SuppressLint
import android.app.Dialog
import android.content.Context
import android.content.res.ColorStateList
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import com.bukuwarung.lib.webview.R
import com.bukuwarung.lib.webview.databinding.DialogCompressionProgressBinding

class CompressionDialog(context: Context) : Dialog(context) {

    private var _binding: DialogCompressionProgressBinding? = null
    private val binding get() = _binding!!

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        _binding = DialogCompressionProgressBinding.inflate(layoutInflater)
        setContentView(binding.root)
        window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        setMinWidth()
        setCancelable(false)
    }

    fun setTitleText(title: String) {
        binding.tvTitle.text = title
    }

    private fun setMinWidth() {
        val minWidth = (context.resources.displayMetrics.widthPixels * 0.90).toInt()
        window?.setLayout(minWidth, ViewGroup.LayoutParams.WRAP_CONTENT)
        window?.setGravity(Gravity.CENTER)
    }

    @SuppressLint("SetTextI18n")
    fun updateProgress(progress: Int) {
        binding.progress.text = "$progress%"
        binding.progressHorizontal.progress = progress
    }

    fun uploadSuccess() {
        binding.tvTitle.text = context.getString(R.string.upload_berhasil)
        binding.progress.text = ""
        binding.ivUploadSuccess.visibility = View.VISIBLE
        binding.progressHorizontal.progress = 100
        binding.progressHorizontal.progressTintList =
            ColorStateList.valueOf(ContextCompat.getColor(context, R.color.green_60))
    }
}
