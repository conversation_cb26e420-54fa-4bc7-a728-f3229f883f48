package com.bukuwarung.lib.webview.util

import com.google.android.gms.location.Priority

object Constant {

    const val TYPE_KTP = "KTP"
    const val TYPE_BANK = "BANK"
    const val TYPE_NPWP = "NPWP"
    const val TYPE_BPJS = "BPJS"
    const val TYPE_FAMILY_PHOTO = "FAMILY_PHOTO"
    const val TYPE_SELFIE_KTP = "SELFIE_WITH_KTP"
    const val TYPE_MH_TICKET = "MH_TICKET"
    const val TYPE_KYB_STORE = "KYB_STORE"
    const val TYPE_PHYSICAL_VISIT_STORE = "PHYSICAL_VISIT_STORE"
    const val TYPE_KYB_INVENTORY = "KYB_INVENTORY"

    /**
     * Note: We use different constants to handle different KYC flows
     * 1. TYPE_BASIC_KYC -> When user is doing KYC for the first time, after selfie upload we
     * redirect to Home and finish WebViewActivity
     * 2. TYPE_BASIC_QRIS -> After selfie upload we redirect user to QRIS form mweb
     * 3. TYPE_KYC_W_DOCS -> After selfie upload we redirect user to additional docs mweb
     * 4. TYPE_QRIS_W_DOCS -> After selfie upload we redirect user to additional docs mweb with query
     * param as isFromQris = true
     */
    const val TYPE_BASIC_KYC = "BASIC_KYC"
    const val TYPE_BMU_RE_KYC = "BMU_RE_KYC"
    const val TYPE_KYC_EDC_ORDER_DETAIL = "KYC_EDC_ORDER_DETAIL"
    const val TYPE_BASIC_QRIS = "BASIC_QRIS"
    const val TYPE_KYC_W_DOCS = "KYC_W_DOCS"
    const val TYPE_QRIS_W_DOCS = "QRIS_W_DOCS"
    const val TYPE_IS_LENDING = "IS_LENDING"
    const val TYPE_IS_BNPL_REGISTRATION_PPOB = "IS_BNPL_REGISTRATION_PPOB"
    const val TYPE_IS_BNPL_REGISTRATION_COMMERCE = "IS_BNPL_REGISTRATION_COMMERCE"
    const val TYPE_IS_BNPL = "IS_BNPL"
    const val TYPE_STORE_FRONT = "STORE_FRONT"
    const val TYPE_IN_STORE = "IN_STORE"
    val newKycFlowConstants =
        arrayListOf(
            TYPE_BASIC_KYC,
            TYPE_BMU_RE_KYC,
            TYPE_KYC_EDC_ORDER_DETAIL,
            TYPE_BASIC_QRIS,
            TYPE_KYC_W_DOCS,
            TYPE_QRIS_W_DOCS,
            TYPE_IS_LENDING,
            TYPE_IS_BNPL_REGISTRATION_COMMERCE,
            TYPE_IS_BNPL_REGISTRATION_PPOB,
            TYPE_IS_BNPL
        )
    val lendingConstants =
        arrayListOf(
            TYPE_STORE_FRONT,
            TYPE_IN_STORE
        )
    const val KYC = "KYC"
    const val KYB = "KYB"
    const val QRIS = "QRIS"
    const val KYC_LENDING = "KYC_LENDING"
    const val KYC_SUBMITTED = "kycSubmitted"
    const val KYB_SUBMITTED = "kyb_submitted"
    const val QRIS_SUBMITTED = "qris_submitted"
    const val QRIS_BANK_SET_SELF = "qris_bank_set_self"
    const val QRIS_BANK_SET_OTHER = "qris_bank_set_other"
    const val APPEAL_FLOW = "appeal_flow"
    const val SALDO_TOP_UP = "saldo_top_up"
    const val EVENT_PROPS = "event_props"

    const val HELP_LINK = "https://bukuwarung.com/tentang-kyc/"
    const val DATA_PRIVACY_LINK = "https://bukuwarung.com/kebijakan-privacy-kyc/"

    const val PORTRAIT = "portrait"
    const val LANDSCAPE = "landscape"
    const val ADDRESS = "ADDRESS"

    const val VERY_LOW = "very_low"
    const val LOW = "low"
    const val MEDIUM = "medium"
    const val HIGH = "high"

    const val SD = "sd"
    const val HD = "hd"
    const val FHD = "fhd"

    const val MIME_TYPE_JPEG = "image/jpeg"

    const val IMAGE_QUALITY_DEFAULT = 80
    const val MEGA_BYTE_1 = 1024f
    const val MEGA_BYTE_1_5 = 1536f
    const val SIZE_2MB = 2

    const val FILE_TYPE_IMAGE = "IMAGE"
    const val FILE_TYPE_PDF = "PDF"
    const val FILE_TYPE_pdf = "pdf"
    const val FILE_TYPE_NONE = "NONE"

    const val IMAGE_EXT_JPG = "jpg"
    const val IMAGE_EXT_JPEG = "jpeg"
    const val IMAGE_EXT_PNG = "png"
    const val IMAGE_EXT_GIF = "gif"

    const val DEV_ENV = "dev"
    const val STAGING_ENV = "stg"
    const val PROD_ENV = "prod"

    enum class LivenessVendor {
        VIDA, PRIVY
    }

    const val DEFAULT_INTERVAL: Long = 30
    const val DEFAULT_FASTEST_INTERVAL: Long = 10
    const val DEFAULT_MAX_WAIT_TIME: Long = 60
    const val DEFAULT_PRIORITY = Priority.PRIORITY_HIGH_ACCURACY

    const val USE_CASE = "use_case"
    const val PHYSICAL_VISIT = "physical_visit"
}
