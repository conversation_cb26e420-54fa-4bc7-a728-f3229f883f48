package com.bukuwarung.lib.webview.util

import android.content.*
import android.net.Uri
import android.os.Build
import android.text.Html
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.style.ClickableSpan
import android.widget.TextView
import android.widget.Toast
import androidx.browser.customtabs.CustomTabsIntent
import androidx.core.content.ContextCompat
import androidx.core.text.bold
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.bukuwarung.lib.webview.R
import java.io.File
import java.util.*

object Utils {

    val BW_URL = listOf(
            "https://api-dev.bukuwarung.com/payments-mweb/",
            "https://api-staging-v1.bukuwarung.com/payments-web/",
            "https://api.bud.bukuwarung.com/payments-mweb/",
            "https://api-dev.bukuwarung.com/bizfund-web/",
            "https://api-staging-v1.bukuwarung.com/bizfund-web/",
            "https://api.bud.bukuwarung.com/bizfund-web/",
            "https://api-dev.bukuwarung.com/los-web/",
            "https://api-staging-v1.bukuwarung.com/los-web/",
            "https://api.bud.bukuwarung.com/los-web/",
            "https://bizfund-web.dev.bukuwarung.com/",
            "https://api-v4.bukuwarung.com/los-web/",
            "https://api-v4.bukuwarung.com/bizfund-web/",
            "https://api-v4.bukuwarung.com/payments-mweb/"
    )

    fun logException(context: Context, ex: Exception) {
        val eventIntent = Intent("com.bukuwarung.action.ACTION_LOG_EXCEPTION").apply {
            putExtra("exception", ex)
        }
        LocalBroadcastManager.getInstance(context).sendBroadcast(eventIntent)
    }

    fun openUrl(context: Context, url: String) {
        val urlIntent = Intent("com.bukuwarung.action.ACTION_OPEN_URL").apply {
            putExtra("url", url)
        }
        LocalBroadcastManager.getInstance(context).sendBroadcast(urlIntent)
    }

    fun copyToClipboard(
        text: String?, context: Context?, toastText: String? = null, showToast: Boolean = true
    ) {
        context ?: return
        val clipboard = context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
        val clip = ClipData.newPlainText("Data", text)
        clipboard.setPrimaryClip(clip)
        if (showToast) {
            Toast.makeText(
                context,
                toastText ?: context.getString(R.string.text_copied),
                Toast.LENGTH_SHORT
            ).show()
        }
    }

    fun isBwUrl(url: String?): Boolean {
        if (url.isNullOrBlank()) return false
        for (baseUrl in BW_URL) {
            if (url.startsWith(baseUrl)) {
                return true
            }
        }
        return false
    }

    fun makeSectionOfTextClickable(
        completeText: String,
        textToClick: String?,
        actionSpan: ClickableSpan,
        boldClickableText: Boolean = true
    ): SpannableStringBuilder {
        val builder = SpannableStringBuilder()
        builder.append(completeText)
        if (textToClick?.isNotEmpty() == true) {
            if (boldClickableText) {
                builder.bold { textToClick ?: "" }
            }
            val startingIndex = completeText.indexOf(textToClick ?: "")
            val endingIndex = startingIndex + (textToClick?.length ?: 0)
            if (startingIndex > 0 && endingIndex > 0) {
                builder.setSpan(
                    actionSpan, startingIndex, endingIndex,
                    Spannable.SPAN_INCLUSIVE_INCLUSIVE
                )
            }
        }
        return builder
    }

    fun launchBrowser(context: Context, url: String) {
        val builder: CustomTabsIntent.Builder = CustomTabsIntent.Builder()
        builder.enableUrlBarHiding()
        builder.setToolbarColor(ContextCompat.getColor(context, R.color.colorPrimary))

        val customTabsIntent: CustomTabsIntent = builder.build()

        try {
            customTabsIntent.launchUrl(context, Uri.parse(url))
        } catch (e: ActivityNotFoundException) {
            launchUriIntent(context, Uri.parse(url))
        }
    }

    fun launchUriIntent(context: Context, uri: Uri) {
        try {
            val browserIntent = Intent(Intent.ACTION_VIEW, uri)
            context.startActivity(browserIntent)
        } catch (ex: ActivityNotFoundException) {
            Toast.makeText(context, R.string.lib_browser_app_not_found, Toast.LENGTH_SHORT).show()
        }
    }

    fun TextView.textHTML(html: String?) {
        html ?: return
        text = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            Html.fromHtml(html, Html.FROM_HTML_MODE_COMPACT)
        } else {
            Html.fromHtml(html)
        }
    }

    fun createTempFile(context: Context): File {
        val cachedImagesFolder = File(context.cacheDir, "images")
        cachedImagesFolder.mkdirs()
        val fileNm = StringBuilder()
        fileNm.append(Date().time).append(".text")
        return File(cachedImagesFolder, fileNm.toString())
    }

    fun saveBase64StringToFile(context: Context, base64String: String): File {
        val file = createTempFile(context)
        file.writeText(base64String)
        return file
    }

    fun base64StringFromFile(fileName: String): String = File(fileName).readText(Charsets.UTF_8)

    /**
     * Checks if both T1 and T2 are not null and executes the block
     */
    inline fun <T1 : Any, T2 : Any, R : Any> safeLet(p1: T1?, p2: T2?, block: (T1, T2) -> R?): R? {
        return if (p1 != null && p2 != null) block(p1, p2) else null
    }

    /**
     * Checks if both p1, p2, p3 and p4 are not null and executes the block
     */
    inline fun <T1 : Any, T2 : Any, T3 : Any, T4 : Any, R : Any> safeLet(
        p1: T1?,
        p2: T2?,
        p3: T3?,
        p4: T4?,
        block: (T1, T2, T3, T4) -> R?
    ): R? {
        return if (p1 != null && p2 != null && p3 != null && p4 != null) block(p1, p2, p3, p4) else null
    }
}
