package com.bukuwarung.lib.webview.geocoding

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class Address(
    @SerializedName("city") var city: String,
    @SerializedName("district") var district: String,
    @SerializedName("id") val id: String,
    @SerializedName("name") var name: String,
    @SerializedName("postalCode") var postalCode: String,
    @SerializedName("province") var province: String,
    @SerializedName("subdistrict") var subDistrict: String,
    @SerializedName("full_address") var fullAddress: String? = "",
    @SerializedName("latitude") var latitude: Double? = null,
    @SerializedName("longitude") var longitude: Double? = null
): Parcelable