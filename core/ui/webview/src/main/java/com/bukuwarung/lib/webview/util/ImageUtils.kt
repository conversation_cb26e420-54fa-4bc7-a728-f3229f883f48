package com.bukuwarung.lib.webview.util

import android.app.Activity
import android.content.ContentResolver
import android.content.Context
import android.graphics.*
import android.media.ExifInterface
import android.net.Uri
import android.os.Build
import com.bukuwarung.lib.webview.R
import com.bukuwarung.lib.webview.util.Constant.FILE_TYPE_IMAGE
import com.bukuwarung.lib.webview.util.Constant.FILE_TYPE_NONE
import com.bukuwarung.lib.webview.util.Constant.FILE_TYPE_PDF
import com.bukuwarung.lib.webview.util.Constant.FILE_TYPE_pdf
import com.bukuwarung.lib.webview.util.Constant.IMAGE_EXT_GIF
import com.bukuwarung.lib.webview.util.Constant.IMAGE_EXT_JPEG
import com.bukuwarung.lib.webview.util.Constant.IMAGE_EXT_JPG
import com.bukuwarung.lib.webview.util.Constant.IMAGE_EXT_PNG
import com.bukuwarung.lib.webview.util.Utils.safeLet
import id.zelory.compressor.Compressor
import id.zelory.compressor.constraint.quality
import id.zelory.compressor.constraint.resolution
import id.zelory.compressor.constraint.size
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.runBlocking
import java.io.*
import java.util.*

object ImageUtils {

    fun compressImage(
        context: Context, imageFile: File, sizeLimit: Long, imageQuality: Int,
        width: Int?, height: Int?
    ): File {
        return runBlocking(Dispatchers.IO) {
            Compressor.compress(context, imageFile) {
                quality(imageQuality)
                size(sizeLimit)
                safeLet(width, height) { width, height -> resolution(width, height) }
            }
        }
    }

    fun compressImage(filePaths: String, maxSize: Float = 2048f): Bitmap? {
        val options = BitmapFactory.Options()

        //      by setting this field as true, the actual bitmap pixels are not loaded in the memory. Just the bounds are loaded. If
        //      you try the use the bitmap here, you will get null.
        options.inJustDecodeBounds = true
        var bmp: Bitmap? = BitmapFactory.decodeFile(filePaths, options)

        var actualHeight = options.outHeight
        var actualWidth = options.outWidth

        //      max Height and width values of the compressed image is taken as 816x612

        val maxHeight = maxSize
        val maxWidth = maxSize
        var imgRatio = actualWidth.toFloat() / actualHeight.toFloat()
        val maxRatio = maxWidth / maxHeight

        //      width and height values are set maintaining the aspect ratio of the image

        // we use compression to a fixed height/width
        if (actualHeight > maxHeight || actualWidth > maxWidth) {
            if (imgRatio < maxRatio) {
                imgRatio = maxHeight / actualHeight
                actualWidth = (imgRatio * actualWidth).toInt()
                actualHeight = maxHeight.toInt()
            } else if (imgRatio > maxRatio) {
                imgRatio = maxWidth / actualWidth
                actualHeight = (imgRatio * actualHeight).toInt()
                actualWidth = maxWidth.toInt()
            } else {
                actualHeight = maxHeight.toInt()
                actualWidth = maxWidth.toInt()
            }
        }

        //TODO: if you want to use sample size instead
        //      setting inSampleSize value allows to load a scaled down version of the original image
//        options.inSampleSize = calculateInSampleSize(options, actualWidth, actualHeight)

        //      inJustDecodeBounds set to false to load the actual bitmap
        options.inJustDecodeBounds = false

        //      this options allow android to claim the bitmap memory if it runs low on memory
        options.inPurgeable = true
        options.inInputShareable = true
        options.inTempStorage = ByteArray(16 * 1024)

        try {
            bmp = BitmapFactory.decodeFile(filePaths, options)
        } catch (exception: OutOfMemoryError) {
            exception.printStackTrace()
        }
        if (bmp == null) return null
        var scaledBitmap = Bitmap.createBitmap(actualWidth, actualHeight, Bitmap.Config.ARGB_8888)

        val ratioX = actualWidth / options.outWidth.toFloat()
        val ratioY = actualHeight / options.outHeight.toFloat()
        val middleX = actualWidth / 2.0f
        val middleY = actualHeight / 2.0f

        val scaleMatrix = Matrix()
        scaleMatrix.setScale(ratioX, ratioY, middleX, middleY)

        val canvas = Canvas(scaledBitmap)
        canvas.setMatrix(scaleMatrix)
        canvas.drawBitmap(
            bmp,
            middleX - bmp.width / 2,
            middleY - bmp.height / 2,
            Paint(Paint.FILTER_BITMAP_FLAG)
        )

        //      check the rotation of the image and display it properly
        val orientation: Int
        try {
            val exif = ExifInterface(filePaths)
            orientation = exif.getAttributeInt(
                ExifInterface.TAG_ORIENTATION, 0
            )
            val matrix = Matrix()
            when (orientation) {
                ExifInterface.ORIENTATION_ROTATE_90 -> matrix.postRotate(90F)
                ExifInterface.ORIENTATION_ROTATE_180 -> matrix.postRotate(180F)
                ExifInterface.ORIENTATION_ROTATE_270 -> matrix.postRotate(270F)
            }
            if (scaledBitmap != null) {
                scaledBitmap = Bitmap.createBitmap(
                    scaledBitmap, 0, 0,
                    scaledBitmap.width, scaledBitmap.height, matrix,
                    true
                )
            }
        } catch (e: IOException) {
            e.printStackTrace()
        }
        var out: FileOutputStream? = null
        try {
            out = FileOutputStream(filePaths)
            scaledBitmap?.compress(Bitmap.CompressFormat.JPEG, 100, out)
            //          write the compressed bitmap at the destination specified by filename.
        } catch (e: FileNotFoundException) {
            e.printStackTrace()
        }
        return scaledBitmap
    }

    private fun calculateInSampleSize(
        options: BitmapFactory.Options,
        reqWidth: Int,
        reqHeight: Int
    ): Int {
        val height = options.outHeight
        val width = options.outWidth
        var inSampleSize = 1

        if (height > reqHeight || width > reqWidth) {
            val heightRatio = Math.round(height.toFloat() / reqHeight.toFloat())
            val widthRatio = Math.round(width.toFloat() / reqWidth.toFloat())
            inSampleSize = if (heightRatio < widthRatio) heightRatio else widthRatio
        }
        val totalPixels = (width * height).toFloat()
        val totalReqPixelsCap = (reqWidth * reqHeight * 2).toFloat()
        while (totalPixels / (inSampleSize * inSampleSize) > totalReqPixelsCap) {
            inSampleSize++
        }
        return inSampleSize
    }

    var outPutFile: File? = null

    @Throws(IOException::class)
    fun generateLocalFile(
        contentResolver: ContentResolver,
        outputDirectory: File,
        uri: Uri?
    ): String? {
        outPutFile = File(outputDirectory, "${Date().time}.pdf")
        if (uri == null) return null
        contentResolver.openInputStream(uri)?.use { inputStream ->
            BufferedReader(InputStreamReader(inputStream)).use { reader ->
                var line: String? = reader.readLine()
                while (line != null) {
                    writeDocument(contentResolver, line)
                    line = reader.readLine()
                }
            }
        }
        return outPutFile?.absolutePath
    }

    private fun getFileUri(): Uri? {
        return outPutFile?.let {
            Uri.fromFile(outPutFile)
        } ?: null

    }


    private fun writeDocument(contentResolver: ContentResolver, line: String) {
        val outputFileUri: Uri? = getFileUri()
        if (outputFileUri == null) return
        try {
            contentResolver.openFileDescriptor(outputFileUri, "wa")?.use {
                FileOutputStream(it.fileDescriptor).use {
                    it.write(line.toByteArray())
                }
            }
        } catch (e: FileNotFoundException) {
            e.printStackTrace()
        } catch (e: IOException) {
            e.printStackTrace()
        }
    }

    fun getFileType(file: File): String {
        val name = file.name
        var ext: String? = null
        val i = name.lastIndexOf('.')
        if (i > 0 && i < name.length - 1) {
            ext = name.substring(i + 1).toLowerCase()
        }
        return when (ext) {
            IMAGE_EXT_JPG, IMAGE_EXT_JPEG, IMAGE_EXT_PNG, IMAGE_EXT_GIF -> FILE_TYPE_IMAGE
            FILE_TYPE_PDF, FILE_TYPE_pdf -> FILE_TYPE_PDF
            else -> FILE_TYPE_NONE
        }
    }

    enum class UploadValidationError {
        MAX_SIZE_EXCEEDED, MIN_SIZE_NOT_MET,
        MAX_RESOLUTION_EXCEEDED, MIN_RESOLUTION_NOT_MET
    }

    /**
     * Runs validations on image and returns error message if there is any validation error.
     * 
     * If validations are not imposed from the remote config, no validation check will be done and
     * null will be returned.
     *
     * @param useCase -> For which purpose file is uploaded e.g. KTP, Selfie, MH ticket etc.
     */
    fun performImageValidations(
        activity: Activity, file: File, uploadValidations: UploadsValidation?, useCase: String
    ): UploadValidationError? {
        val props = mutableMapOf<String?, Any?>()
        props[AnalyticsUtil.USE_CASE] = useCase
        // Run size validations
        uploadValidations?.maxFileSize?.let {
            if (file.length() > it) {
                props[AnalyticsUtil.ERROR_TYPE] = UploadValidationError.MAX_SIZE_EXCEEDED
                AnalyticsUtil.logEvent(
                    activity,
                    AnalyticsUtil.EVENT_VALIDATION_ERROR,
                    AnalyticsUtil.getJsonStringOfMap(props)
                )
                Utils.logException(
                    activity, Exception("${UploadValidationError.MAX_SIZE_EXCEEDED} in $useCase")
                )
                return UploadValidationError.MAX_SIZE_EXCEEDED
            }
        }
        uploadValidations?.minFileSize?.let {
            if (file.length() < it) {
                props[AnalyticsUtil.ERROR_TYPE] = UploadValidationError.MIN_SIZE_NOT_MET
                AnalyticsUtil.logEvent(
                    activity,
                    AnalyticsUtil.EVENT_VALIDATION_ERROR,
                    AnalyticsUtil.getJsonStringOfMap(props)
                )
                Utils.logException(
                    activity, Exception("${UploadValidationError.MIN_SIZE_NOT_MET} in $useCase")
                )
                return UploadValidationError.MIN_SIZE_NOT_MET
            }
        }

        if (getFileType(file) != FILE_TYPE_IMAGE) return null

        // Run resolution validations
        val imageRes = getImageResolution(activity, Uri.fromFile(file))
        uploadValidations?.maxImageResolution?.let {
            if (imageRes.height > it.height || imageRes.width > it.width) {
                props[AnalyticsUtil.ERROR_TYPE] = UploadValidationError.MAX_RESOLUTION_EXCEEDED
                AnalyticsUtil.logEvent(
                    activity,
                    AnalyticsUtil.EVENT_VALIDATION_ERROR,
                    AnalyticsUtil.getJsonStringOfMap(props)
                )
                Utils.logException(
                    activity, Exception("${UploadValidationError.MAX_RESOLUTION_EXCEEDED} in $useCase")
                )
                return UploadValidationError.MAX_RESOLUTION_EXCEEDED
            }
        }
        uploadValidations?.minImageResolution?.let {
            if (imageRes.height < it.height || imageRes.width < it.width) {
                props[AnalyticsUtil.ERROR_TYPE] = UploadValidationError.MIN_RESOLUTION_NOT_MET
                AnalyticsUtil.logEvent(
                    activity,
                    AnalyticsUtil.EVENT_VALIDATION_ERROR,
                    AnalyticsUtil.getJsonStringOfMap(props)
                )
                Utils.logException(
                    activity, Exception("${UploadValidationError.MIN_RESOLUTION_NOT_MET} in $useCase")
                )
                return UploadValidationError.MIN_RESOLUTION_NOT_MET
            }
        }
        return null
    }

    /**
     * Returns resolution of the image.
     */
    private fun getImageResolution(activity: Activity, uri: Uri): Resolution {
        val options = BitmapFactory.Options().apply { inJustDecodeBounds = true }
        BitmapFactory.decodeStream(
            activity.contentResolver.openInputStream(uri), null, options
        )
        return Resolution(options.outWidth, options.outHeight)
    }

    /**
     * Returns error message according to the validation error that occurred.
     * Returns null if there is no error.
     */
    fun getValidationErrorMessage(
        context: Context,
        validationError: UploadValidationError?,
        validations: UploadsValidation?
    ): String? {
        return when (validationError) {
            UploadValidationError.MAX_SIZE_EXCEEDED -> {
                validations?.maxFileSize?.let {
                    context.getString(
                        R.string.aw_max_size_limit_message,
                        getFileSizeString(context, it)
                    )
                }
            }
            UploadValidationError.MIN_SIZE_NOT_MET -> {
                validations?.minFileSize?.let {
                    context.getString(
                        R.string.aw_min_size_limit_message,
                        getFileSizeString(context, it)
                    )
                }
            }
            UploadValidationError.MAX_RESOLUTION_EXCEEDED -> {
                context.getString(
                    R.string.aw_max_res_limit_message,
                    validations?.maxImageResolution?.width,
                    validations?.maxImageResolution?.height
                )
            }
            UploadValidationError.MIN_RESOLUTION_NOT_MET -> {
                context.getString(
                    R.string.aw_min_res_limit_message,
                    validations?.minImageResolution?.width,
                    validations?.minImageResolution?.height
                )
            }
            else -> null
        }
    }

    private fun getFileSizeString(context: Context, size: Long): String {
        return when {
            // If size is below 1MB, we show xyzKB
            size < Constant.MEGA_BYTE_1 * Constant.MEGA_BYTE_1 -> {
                context.getString(
                    R.string.aw_d_KB,
                    size.div(Constant.MEGA_BYTE_1).toInt()
                )
            }
            else -> {
                // If size is above 1MB and a whole number, we show xMB
                if (size % Constant.MEGA_BYTE_1 == 0f) {
                    context.getString(
                        R.string.aw_d_MB,
                        size.div(Constant.MEGA_BYTE_1).div(Constant.MEGA_BYTE_1).toInt()
                    )
                } else {
                    // If size is above 1MB and in decimals, we show x.yMB
                    context.getString(
                        R.string.aw_s_MB,
                        String.format(
                            "%.1f",
                            size.div(Constant.MEGA_BYTE_1).div(Constant.MEGA_BYTE_1)
                        )
                    )
                }
            }
        }
    }

    /**
     * Finds recommended resolution applicable for the current device SDK
     * If there is no specific resolution available for this SDK, resolution from next matching
     * SDK will be used.
     * For breaking condition for the while loop, added 40 instead of using Build.VERSION_CODES.R(30)
     * since latest SDK will continue increasing but developers won't need to update here.
     */
    fun getRecommendedResolution(resolutions: HashMap<Int, Resolution>?): Resolution? {
        var currentSdk = Build.VERSION.SDK_INT
        var recommended: Resolution? = null
        while (currentSdk < 40) {
            recommended = resolutions?.get(currentSdk)
            if (recommended != null) return recommended
            currentSdk++
        }
        return recommended
    }
}