package com.bukuwarung.lib.webview.network

import com.bukuwarung.lib.webview.data.ImageUploads
import okhttp3.MultipartBody
import retrofit2.http.*


interface KycRemoteDataSource {

    @Multipart
    @POST("attachments/additional-video")
    suspend fun uploadKycVideo(
        @Header("Authorization") auth: String?,
        @Part file: MultipartBody.Part
    ): KycVideoUploadResponse

    @POST("v3/checks/face_matching")
    suspend fun uploadKycImages(
        @Header("Authorization") auth: String?,
        @Body imageUploads: ImageUploads
    ): KycSelfieUploadResponse

    @Multipart
    @POST("v3/checks/face_matching/file")
    suspend fun uploadKycSelfieMultipart(
        @Header("buku-origin") bukuOrigin: String,
        @Header("Authorization") auth: String?,
        @Query("provider") provider: KYCProvider?,
        @Query("kyc_flow") kycFlow: String?,
        @Query("re_kyc_type") reKycType: String?,
        @Part file: MultipartBody.Part
    ): KycSelfieUploadResponse

    @POST("kyc/resubmit")
    suspend fun submitKyc(
        @Header("Authorization") auth: String?,
        @Body kyc: KycSubmit
    ): KycResult

    @GET("config/active-providers")
    suspend fun getActiveProviders(
        @Header("Authorization") auth: String?,
        @Header("buku-origin") bukuOrigin: String
    ): ActiveProvidersResponse
}