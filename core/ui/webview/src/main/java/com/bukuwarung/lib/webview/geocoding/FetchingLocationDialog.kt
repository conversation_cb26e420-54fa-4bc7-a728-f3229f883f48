package com.bukuwarung.lib.webview.geocoding

import android.app.Dialog
import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.Gravity
import android.view.ViewGroup
import com.bukuwarung.lib.webview.databinding.DialogFetchingLocationBinding

class FetchingLocationDialog(context: Context) : Dialog(context) {

    private var _binding: DialogFetchingLocationBinding? = null
    private val binding get() = _binding!!

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        _binding = DialogFetchingLocationBinding.inflate(layoutInflater)
        setContentView(binding.root)
        window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        setMinWidth()
        setCancelable(false)
    }

    private fun setMinWidth() {
        val minWidth = (context.resources.displayMetrics.widthPixels * 0.90).toInt()
        window?.setLayout(minWidth, ViewGroup.LayoutParams.WRAP_CONTENT)
        window?.setGravity(Gravity.CENTER)
    }
}