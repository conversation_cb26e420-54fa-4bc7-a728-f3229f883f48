package com.bukuwarung.lib.webview.camera

import android.Manifest
import android.app.Activity
import android.content.ContentValues
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.MediaStore
import android.view.View
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.camera.core.Camera
import androidx.camera.core.CameraSelector
import androidx.camera.core.Preview
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.video.*
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.core.content.PermissionChecker
import com.abedelazizshe.lightcompressorlibrary.CompressionListener
import com.abedelazizshe.lightcompressorlibrary.VideoCompressor
import com.abedelazizshe.lightcompressorlibrary.VideoQuality
import com.abedelazizshe.lightcompressorlibrary.config.Configuration
import com.abedelazizshe.lightcompressorlibrary.config.SaveLocation
import com.abedelazizshe.lightcompressorlibrary.config.SharedStorageConfiguration
import com.bukuwarung.lib.webview.R
import com.bukuwarung.lib.webview.databinding.ActivityVideoKycBinding
import com.bukuwarung.lib.webview.dialog.VideoAnalyseDialog
import com.bukuwarung.lib.webview.kyc.VideoKycPreviewActivity
import com.bukuwarung.lib.webview.util.Constant
import com.bukuwarung.lib.webview.util.Constant.MEGA_BYTE_1
import com.bukuwarung.lib.webview.util.FileSizeLimits
import com.bukuwarung.lib.webview.util.FileUtils
import com.bukuwarung.lib.webview.util.Utils.textHTML
import java.io.File
import java.text.SimpleDateFormat
import java.util.*


class VideoKycActivity : AppCompatActivity() {

    private var recording: Recording? = null
    private val CAMERA_DIR = "LOCAL"
    private var lensFacing = CameraSelector.LENS_FACING_FRONT
    private var camera: Camera? = null
    private val REQUIRED_PERMISSIONS = arrayOf(
        Manifest.permission.CAMERA,
        Manifest.permission.RECORD_AUDIO,
        Manifest.permission.WRITE_EXTERNAL_STORAGE
    )
    private val REQUEST_CODE_PERMISSIONS = 10
    private lateinit var outputDirectory: File
    private lateinit var cameraProvider: ProcessCameraProvider
    private lateinit var videoCapture: VideoCapture<Recorder>
    private lateinit var binding: ActivityVideoKycBinding

    private val inputIds by lazy { intent?.getStringExtra(DOCUMENT_ID) }
    private val isFromQris by lazy { intent?.getBooleanExtra(FROM_QRIS, false) }
    private val token by lazy { intent?.getStringExtra(TOKEN) }
    private val baseUrl by lazy { intent?.getStringExtra(BASE_URL) }
    private val authUrl by lazy { intent?.getStringExtra(AUTH_URL) }
    private val sessionToken by lazy { intent?.getStringExtra(SESSION_TOKEN) }
    private val ktpName by lazy { intent?.getStringExtra(KTP_NAME) }
    private val sizeLimit by lazy {
        intent?.getLongExtra(FILE_SIZE_LIMIT, FileSizeLimits().VIDEO) ?: FileSizeLimits().VIDEO
    }
    private val videoQuality by lazy { intent?.getStringExtra(VIDEO_QUALITY) }
    private val compressionQuality by lazy { intent?.getStringExtra(COMPRESSION_QUALITY) }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityVideoKycBinding.inflate(layoutInflater)
        setContentView(binding.root)
        outputDirectory = getOutputDirectory()

        prepareCamera()

        binding.btnCameraRecord.setOnClickListener {
            captureVideo()
        }
        binding.btnBack.setOnClickListener {
            super.onBackPressed()
        }
    }

    private fun prepareCamera(quality: Quality = getVideoQuality()) {
        // Request camera permissions
        if (allPermissionsGranted()) {
            startCameraForVideo(quality)
        } else {
            ActivityCompat.requestPermissions(
                this@VideoKycActivity,
                REQUIRED_PERMISSIONS,
                REQUEST_CODE_PERMISSIONS
            )
        }
    }

    private fun captureVideo() {
        val videoCapture = if (::videoCapture.isInitialized) this.videoCapture else return

        binding.btnCameraRecord.isEnabled = false

        val curRecording = recording
        if (curRecording != null) {
            // Stop the current recording session.
            curRecording.stop()
            recording = null
            timer?.cancel()
            return
        } else {
            startTimer()
        }

        val fileNameFormat = "yyyy_mm_dd_hh_ss"
        // create and start a new recording session
        val name = SimpleDateFormat(fileNameFormat, Locale.US)
            .format(System.currentTimeMillis())
        val contentValues = ContentValues().apply {
            put(MediaStore.MediaColumns.DISPLAY_NAME, name)
            put(MediaStore.MediaColumns.MIME_TYPE, "video/mp4")
            if (Build.VERSION.SDK_INT > Build.VERSION_CODES.P) {
                put(MediaStore.Video.Media.RELATIVE_PATH, "Movies/CameraX-Video")
            }
        }

        val mediaStoreOutputOptions = MediaStoreOutputOptions
            .Builder(contentResolver, MediaStore.Video.Media.EXTERNAL_CONTENT_URI)
            .setContentValues(contentValues)
            .build()
        recording = videoCapture.output
            .prepareRecording(this, mediaStoreOutputOptions)
            .apply {
                if (PermissionChecker.checkSelfPermission(
                        this@VideoKycActivity,
                        Manifest.permission.RECORD_AUDIO
                    ) == PermissionChecker.PERMISSION_GRANTED
                ) {
                    withAudioEnabled()
                }
            }
            .start(ContextCompat.getMainExecutor(this)) { recordEvent ->
                when (recordEvent) {
                    is VideoRecordEvent.Start -> {
                        with(binding) {
                            btnCameraRecord.apply {
                                setBackgroundResource(R.drawable.ic_capture_video_start)
                                isEnabled = true
                            }
                            btnContainer.setBackgroundColor(
                                ContextCompat.getColor(
                                    this@VideoKycActivity,
                                    R.color.black_transparent
                                )
                            )
                            toolbar.visibility = View.GONE
                            binding.tvDisclaimer.textHTML(
                                getString(
                                    R.string.kyc_video_statement,
                                    ktpName
                                )
                            )
                            tvTimer.setCompoundDrawablesWithIntrinsicBounds(
                                R.drawable.ic_record, 0, 0, 0
                            )
                        }
                    }
                    is VideoRecordEvent.Finalize -> {
                        if (!recordEvent.hasError()) {
                            try {
                                compressVideo(recordEvent.outputResults.outputUri)
                            } catch (ex: Exception) {
                                handleResult(recordEvent.outputResults.outputUri, null, false)
                            }
                        } else {
                            recording?.close()
                            recording = null
                            Toast.makeText(this, "Error: ${recordEvent.cause}", Toast.LENGTH_SHORT)
                                .show()
                        }
                        binding.btnCameraRecord.apply {
                            setBackgroundResource(R.drawable.ic_capture_video)
                            isEnabled = true
                        }
                    }
                }
            }
    }

    private fun startCameraForVideo(quality: Quality = getVideoQuality()) {
        val cameraProviderFuture = ProcessCameraProvider.getInstance(this)

        cameraProviderFuture.addListener({
            // Used to bind the lifecycle of cameras to the lifecycle owner
            cameraProvider = cameraProviderFuture.get()
            val cameraSelector = CameraSelector.Builder().requireLensFacing(lensFacing).build()

            // Preview
            val preview = Preview.Builder()
                .build()
                .also {
                    it.setSurfaceProvider(binding.previewCamera.surfaceProvider)
                }

            val qualitySelector = QualitySelector.from(quality)
            val recorder = Recorder.Builder()
                .setQualitySelector(qualitySelector)
                .build()
            videoCapture = VideoCapture.withOutput(recorder)

            try {
                // Unbind use cases before rebinding
                cameraProvider.unbindAll()
                // Bind use cases to camera
                camera = cameraProvider.bindToLifecycle(this, cameraSelector, preview, videoCapture)
            } catch (ex: Exception) {
                Toast.makeText(
                    this,
                    "Error: ${ex.localizedMessage}",
                    Toast.LENGTH_SHORT
                ).show()
            }
        }, ContextCompat.getMainExecutor(this))
    }

    private fun getOutputDirectory(): File {
        val mediaDir = externalMediaDirs.firstOrNull()?.let {
            File(it, CAMERA_DIR).apply { mkdirs() }
        }
        return if (mediaDir != null && mediaDir.exists())
            mediaDir else filesDir
    }

    private fun allPermissionsGranted() = REQUIRED_PERMISSIONS.all {
        ContextCompat.checkSelfPermission(baseContext, it) == PackageManager.PERMISSION_GRANTED
    }

    var timer: Timer? = null
    private fun startTimer() {
        binding.tvTimer.visibility = View.VISIBLE
        timer = Timer()
        var time = 10

        timer?.scheduleAtFixedRate(object : TimerTask() {
            override fun run() {
                runOnUiThread {
                    if (time < 0) {
                        timer?.cancel()
                        captureVideo() // Stop Recording
                    } else
                        setTime(time)
                }
                time--
            }
        }, 1000, 1000)
    }

    private fun setTime(time: Int) {
        binding.tvTimer.text = String.format(
            getString(R.string.timer_video_record),
            padding(time)
        )
    }

    private fun padding(number: Int): String {
        return if (number <= 9) "0${number}" else "$number"
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        when (requestCode) {
            REQUEST_CODE_PERMISSIONS -> {
                if (allPermissionsGranted()) {
                    startCameraForVideo()
                } else {
                    Toast.makeText(
                        this@VideoKycActivity,
                        getString(R.string.video_record_error),
                        Toast.LENGTH_SHORT
                    ).show()
                    finish()
                }
            }
        }
    }

    companion object {
        const val DOCUMENT_ID = "doc_id"
        const val TOKEN = "token"
        const val BASE_URL = "base_url"
        const val KTP_NAME = "ktp_name"
        const val FROM_QRIS = "is_from_qris"
        const val ACTION = "ACTION"
        private const val AUTH_URL = "auth_url"
        private const val SESSION_TOKEN = "session_token"
        private const val FILE_SIZE_LIMIT = "file_size_limit"
        private const val VIDEO_QUALITY = "video_quality"
        private const val COMPRESSION_QUALITY = "compression_quality"
        private const val REQUEST_VIDEO_PREVIEW = 11

        fun createIntent(
            origin: Context?, inputIds: String?, isFromQris: Boolean?,
            token: String?, baseUrl: String?, ktpName: String?,
            authUrl: String?, sessionToken: String?, sizeLimit: Long,
            videoQuality: String, compressionQuality: String
        ): Intent {
            val intent = Intent(origin, VideoKycActivity::class.java).apply {
                putExtra(DOCUMENT_ID, inputIds)
                putExtra(TOKEN, token)
                putExtra(FROM_QRIS, isFromQris)
                putExtra(BASE_URL, baseUrl)
                putExtra(KTP_NAME, ktpName)
                putExtra(AUTH_URL, authUrl)
                putExtra(SESSION_TOKEN, sessionToken)
                putExtra(FILE_SIZE_LIMIT, sizeLimit)
                putExtra(VIDEO_QUALITY, videoQuality)
                putExtra(COMPRESSION_QUALITY, compressionQuality)
            }
            return intent
        }
    }

    @Throws(Exception::class)
    private fun compressVideo(videoUri: Uri) {
        val dialog = VideoAnalyseDialog(this@VideoKycActivity)
        VideoCompressor.start(
            context = applicationContext,
            uris = listOf(videoUri),
            isStreamable = true,
            sharedStorageConfiguration = SharedStorageConfiguration(
                    videoName = "kyc-video",
                    saveAt = SaveLocation.pictures,
            ),
            listener = object : CompressionListener {
                override fun onProgress(index: Int, percent: Float) {

                }

                override fun onStart(index: Int) {
                    runOnUiThread {
                        dialog.show()
                    }
                }

                override fun onSuccess(index: Int, size: Long, path: String?) {
                    runOnUiThread {
                        dialog.cancel()
                        handleResult(videoUri, path, true)
                    }
                }

                override fun onFailure(index: Int, failureMessage: String) {
                    runOnUiThread {
                        dialog.cancel()
                        handleResult(videoUri, null, false)
                    }
                }

                override fun onCancelled(index: Int) {
                    runOnUiThread {
                        dialog.cancel()
                        handleResult(videoUri, null, false)
                    }
                }
            },
            configureWith = Configuration(
                quality = getCompressionQuality(),
                isMinBitrateCheckEnabled = false,
                disableAudio = false,
                keepOriginalResolution = true
            )
        )
    }

    override fun onBackPressed() {
        if (timer == null) super.onBackPressed()
    }

    private fun getVideoQuality(): Quality {
        return when (videoQuality) {
            Constant.SD -> Quality.SD
            Constant.HD -> Quality.HD
            Constant.FHD -> Quality.FHD
            else -> Quality.SD
        }
    }

    private fun getCompressionQuality(): VideoQuality {
        return when (compressionQuality) {
            Constant.VERY_LOW -> VideoQuality.VERY_LOW
            Constant.LOW -> VideoQuality.LOW
            Constant.MEDIUM -> VideoQuality.MEDIUM
            Constant.HIGH -> VideoQuality.HIGH
            else -> VideoQuality.LOW
        }
    }

    private fun handleResult(videoUri: Uri, path: String?, compressionSuccess: Boolean) {
        var filePath = path
        if (!compressionSuccess) {
            filePath = FileUtils(this@VideoKycActivity).getPath(videoUri)
        }
        if (filePath == null) {
            Toast.makeText(
                this@VideoKycActivity,
                getString(R.string.lib_couldnt_create_file),
                Toast.LENGTH_SHORT
            ).show()
            return
        }
        val fileSize = File(filePath).length()
        if (isExceedingSizeLimit(fileSize)) {
            Toast.makeText(
                this@VideoKycActivity,
                getString(
                    R.string.lib_file_size_error,
                    "${String.format("%.1f", fileSize / MEGA_BYTE_1 / MEGA_BYTE_1)}MB"
                ),
                Toast.LENGTH_SHORT
            ).show()
            resetUI()
            prepareCamera(Quality.LOWEST)
            return
        }

        startActivityForResult(
            VideoKycPreviewActivity.createIntent(
                this@VideoKycActivity,
                filePath, inputIds, isFromQris, token, baseUrl,
                authUrl, sessionToken
            ), REQUEST_VIDEO_PREVIEW
        )
    }

    private fun isExceedingSizeLimit(fileSize: Long): Boolean {
        return fileSize > sizeLimit
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == REQUEST_VIDEO_PREVIEW) {
            if (resultCode == Activity.RESULT_CANCELED) {
                finish()
            } else if (resultCode == Activity.RESULT_OK) {
                val retake = data?.getBooleanExtra(ACTION, false)
                if (retake == true) {
                    resetUI()
                    prepareCamera()
                } else {
                    val i = Intent().apply { putExtra(FROM_QRIS, isFromQris) }
                    setResult(Activity.RESULT_OK, i)
                    finish()
                }
            }
        }
    }

    private fun resetUI() {
        setTime(10)
        with(binding) {
            btnContainer.setBackgroundColor(
                ContextCompat.getColor(this@VideoKycActivity, R.color.lib_black)
            )
            toolbar.visibility = View.VISIBLE
            binding.tvDisclaimer.text = getString(R.string.video_disclaimer)
            tvTimer.setCompoundDrawablesWithIntrinsicBounds(0, 0, 0, 0)
            timer = null
        }
    }

}