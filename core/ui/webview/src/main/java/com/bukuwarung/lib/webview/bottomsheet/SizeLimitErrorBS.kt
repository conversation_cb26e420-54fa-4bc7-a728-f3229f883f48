package com.bukuwarung.lib.webview.bottomsheet

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.bukuwarung.lib.webview.R
import com.bukuwarung.lib.webview.databinding.SizeLimitBottomSheetBinding
import com.bukuwarung.lib.webview.util.Constant.MEGA_BYTE_1
import com.bukuwarung.lib.webview.util.Constant.SIZE_2MB


class SizeLimitErrorBS : BaseBottomSheetDialogFragment() {

    companion object {
        const val TAG = "SizeLimitErrorBS"
        const val SIZE_LIMIT = "size_limit"
        const val ERROR_MESSAGE = "error_message"

        fun createInstance(sizeLimit: Long, errorMessage: String? = null) =
            SizeLimitErrorBS().apply {
                this.arguments = Bundle().apply {
                    putLong(SIZE_LIMIT, sizeLimit)
                    putString(ERROR_MESSAGE, errorMessage)
                }
            }
    }

    private lateinit var binding: SizeLimitBottomSheetBinding
    private var callback: Callback? = null

    interface Callback {
        fun sizeLimitAction()
    }

    init {
        isCancelable = false
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {

        binding = SizeLimitBottomSheetBinding.inflate(inflater, container, false)

        binding.btnSizeLimitAction.setOnClickListener {
            callback?.sizeLimitAction()
            dismiss()
        }

        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        val sizeLimit = arguments?.getLong(SIZE_LIMIT)
        val message = arguments?.getString(ERROR_MESSAGE)
        if (message != null) {
            binding.tvSizeLimitDescription.text = message
        } else {
            val sizeLimitMB: Int =
                sizeLimit?.div(MEGA_BYTE_1)?.div(MEGA_BYTE_1)?.toInt() ?: SIZE_2MB
            binding.tvSizeLimitDescription.text =
                getString(R.string.lib_size_limit_description, sizeLimitMB)
        }
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        callback = context as? Callback
    }
}
