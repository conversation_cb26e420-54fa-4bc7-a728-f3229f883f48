package com.bukuwarung.lib.webview.data

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import okhttp3.MediaType
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody
import okio.BufferedSink
import java.io.File
import java.io.FileInputStream
import java.io.IOException


class ProgressRequestBody(
    private val mFile: File,
    private val contentType: String,
    private val ignoreFirstNumberOfWriteToCalls: Int
) : RequestBody() {

    companion object {
        private const val DEFAULT_BUFFER_SIZE = 2048
    }

    private var numWriteToCalls = 0
    private val _progress = MutableLiveData<Float>()
    val progress: LiveData<Float> = _progress

    override fun contentType(): MediaType? {
        return contentType.toMediaTypeOrNull()
    }

    override fun contentLength(): Long {
        return mFile.length()
    }

    @Throws(IOException::class)
    override fun writeTo(sink: BufferedSink) {
        val fileLength = mFile.length()
        val buffer = ByteArray(DEFAULT_BUFFER_SIZE)
        val inputStream = FileInputStream(mFile)
        var uploaded: Long = 0
        numWriteToCalls++

        inputStream.use { it ->
            var read: Int
            var lastProgressPercentUpdate = 0.0f
            while (it.read(buffer).also { read = it } != -1) {
                // update progress on UI thread
                uploaded += read.toLong()
                sink.write(buffer, 0, read)
                // when using HttpLoggingInterceptor it calls writeTo and passes data into a local buffer just for logging purposes.
                // the second call to write to is the progress we actually want to track
                if (numWriteToCalls > ignoreFirstNumberOfWriteToCalls) {
                    val progress = (uploaded.toFloat() / fileLength.toFloat()) * 100f
                    if (progress - lastProgressPercentUpdate > 1 || progress == 100f) {
                        _progress.postValue(progress)
                        lastProgressPercentUpdate = progress
                    }
                }
            }
        }
    }
}