package com.bukuwarung.lib.webview.jsinterface

import android.app.Activity
import android.webkit.JavascriptInterface
import java.lang.ref.WeakReference

class BwWebInterface(weakActivity: WeakReference<Activity>, private val listener: BwWebListener?) {
    private var activity: Activity? = null

    init {
        activity = weakActivity.get()
    }

    interface BwWebListener {
        fun onSetTitle(text: String?)
        fun openCamera(type: String?)
        fun openCameraWithParams(type: String?, title: String?, message: String?)
        fun openCameraWithProps(type: String?, props: String?)
        fun openImagePDFPicker()
        fun startLivelinessCheck(productId: String?)
        fun initializeLivelinessCheck(userCase: String?)
        fun onSuccess(type: String?)
        fun onSuccessWithMessage(type: String?, message: String?)
        fun callIntent(url: String?)
        fun webviewGetToken()
        fun handleBackPress()
        fun webGetUserId()
        fun onCloseWebview()
        fun webviewRefreshToken(): String?
        fun showError(isServiceError: Boolean, message: String?)
        fun sendAppsflyerEvent(eventName: String, jsonProp: String?)
        fun sendMoengageEvent(eventName: String, jsonProp: String?)
        fun trackEvent(
            eventName: String,
            jsonProp: String?,
            amplitude: Boolean,
            cleverTap: Boolean,
            firebase: Boolean,
            appsFlyer: Boolean,
            tiktokEventName: String?,
        )
        fun onTokokoEvent(eventName: String, jsonProp: String?)
        fun openGallery()
        fun openRefundBank()
        fun launchActivityForResult(requestType: String)
        fun openKeyboard()
        fun hideKeyboard()
        fun onQrisSubmit(result: String, bookId: String)
        fun selectBankAccount(isForQris: Boolean, setQrisBank: Boolean)
        fun selectQrisBankAccount(bookId: String)
        fun addQrisBankAccount(bookId: String, addingFor: String, addingNewBank: Boolean)
        fun downloadFile(url: String, fileName: String)
        fun getAppVersion()
        fun redirectToPaymentDetail(orderId:String, paymentType:String, isSuccess:Boolean)
        fun openContactBook()
        fun webViewGetUserUUID()
        fun getBnplBookId(): String
        fun getBnplBookName(): String
        fun setFeatureShown(id: String = "")
        fun isFeatureShown(id: String = ""): String
        fun getBWBookId(): String?
        fun getBWBookName(): String?
        fun getSessionValueByKey(id: String = ""): String?
        fun openCustomTab(url: String)
        fun openBookUpdate(bookId: String)
        fun openBookUpdateWithUseCase(bookId: String, useCase: String)
        fun fetchAddress()
        fun openVideoKyc(input:String, isFromQris: Boolean, ktpName: String)
        fun openVideoKycWithProps(input:String, isFromQris: Boolean, ktpName: String, props: String?)
        fun getPhoneNumber(): String?
        fun redirectPaymentDetail(orderId: String, paymentType: String, isSuccess: Boolean, message: String, isFromAssistPage: Boolean)
        fun getSharedPrefValue(id: String = "", type: String = ""): String?
        fun redirectToDedicatedLoanBook()
        fun lockRotation(orientation: String)
        fun timerFinished()
        fun trackEvent(eventName: String = "", eventProp: String = "")
        fun trackUserProperty(propName: String = "", propValue: String = "")
        fun getBWAppToken(): String?
        fun getBWUserId(): String?
        fun getAppVersionCode(): String?
        fun getBWAppVersionName(): String?
        fun isSaldoActivated(): String?
        fun getBWEntryPoint(): String?
        fun openBWActivity(activity: String = "", parameter: String, title: String)
        fun openWhatsappWithMessage(phoneNumber: String = "", message: String = "")
        fun copyToClipboard(text: String = "", toastText:String = "Teks Tersalin!")
        fun shareCalled()
        fun fetchLocationAndImage(imageType: String)
        fun getAppealBankAccount()
        fun fetchRemoteConfig(key: String): String?
        fun openHelpDialog()
        fun openBottomSheet(screenName: String, bottomSheetId: Int)
        fun shareWithOpenTray(message: String?, phoneNumber: String?)
        fun startOtpVerification(phoneNumber: String, countryCode: String, useCase: String)
        fun startPhysicalVisit()
        fun fetchDeviceDetails(): String?
        fun shareOnWhatsapp(phoneNumber: String, message: String, url: String)
        fun openWebview(url: String?)
        fun requestLocation()
        fun redirectVidaSign(url: String?)
        fun openDialog(
            title: String,
            message: String,
            positiveButtonType: String,
            negativeButtonType: String,
            activityUrl: String,
            parameter: String
        )
    }

    @JavascriptInterface
    fun requestLocation(){
        activity?.runOnUiThread {
            listener?.requestLocation()
        }
    }

    @JavascriptInterface
    fun setTitle(text: String?) {
        activity?.runOnUiThread {
            listener?.onSetTitle(text)
        }
    }

    @JavascriptInterface
    fun openCamera(type: String?) {
        activity?.runOnUiThread {
            listener?.openCamera(type)
        }
    }

    @JavascriptInterface
    fun openCameraWithParams(type: String?, title: String?, message: String?) {
        activity?.runOnUiThread {
            listener?.openCameraWithParams(type, title, message)
        }
    }

    @JavascriptInterface
    fun openCameraWithProps(type: String?, props: String?) {
        activity?.runOnUiThread {
            listener?.openCameraWithProps(type, props)
        }
    }

    @JavascriptInterface
    fun openVideo(input:String, isFromQris: Boolean, ktpName: String) {
        activity?.runOnUiThread {
            listener?.openVideoKyc(input, isFromQris, ktpName)
        }
    }

    @JavascriptInterface
    fun openVideoWithProps(input:String, isFromQris: Boolean, ktpName: String, props: String?) {
        activity?.runOnUiThread {
            listener?.openVideoKycWithProps(input, isFromQris, ktpName, props)
        }
    }

    @JavascriptInterface
    fun openImagePDFPicker() {
        activity?.runOnUiThread {
            listener?.openImagePDFPicker()
        }
    }

    @JavascriptInterface
    fun startLivelinessCheck(productId: String?) {
        activity?.runOnUiThread {
            listener?.startLivelinessCheck(productId)
        }
    }

    @JavascriptInterface
    fun initializeLivelinessCheck(useCase: String?) {
        activity?.runOnUiThread {
            listener?.initializeLivelinessCheck(useCase)
        }
    }

    @JavascriptInterface
    fun openRefundBank() {
        activity?.runOnUiThread {
            listener?.openRefundBank()
        }
    }

    @JavascriptInterface
    fun openGallery() {
        activity?.runOnUiThread {
            listener?.openGallery()
        }
    }

    @JavascriptInterface
    fun closeWebview() {
        activity?.runOnUiThread {
            listener?.onCloseWebview()
        }
    }

    @JavascriptInterface
    fun callIntent(url: String?) {
        activity?.runOnUiThread {
            listener?.callIntent(url)
        }
    }

    @JavascriptInterface
    fun onSuccess(type: String?) {
        activity?.runOnUiThread {
            listener?.onSuccess(type)
        }
    }

    @JavascriptInterface
    fun onSuccessWithMessage(type: String?, message: String?) {
        activity?.runOnUiThread {
            listener?.onSuccessWithMessage(type, message)
        }
    }

    @JavascriptInterface
    fun androidHandleBackPressed() {
        activity?.runOnUiThread {
            listener?.handleBackPress()
        }
    }

    @JavascriptInterface
    fun getUserID() {
        activity?.runOnUiThread {
            listener?.webGetUserId()
        }
    }

    @JavascriptInterface
    fun refreshToken(): String? {
        return listener?.webviewRefreshToken()
    }

    @JavascriptInterface
    fun showError(isServiceError: Boolean, message: String?) {
        activity?.runOnUiThread {
            listener?.showError(isServiceError, message)
        }
    }

    @JavascriptInterface
    fun getToken() {
        activity?.runOnUiThread {
            listener?.webviewGetToken()
        }
    }

    @JavascriptInterface
    fun getUserUUID() {
        activity?.runOnUiThread {
            listener?.webViewGetUserUUID()
        }
    }

    @JavascriptInterface
    fun sendAppsflyerEvent(eventName: String, jsonProp: String?) {
        activity?.runOnUiThread {
            listener?.sendAppsflyerEvent(eventName, jsonProp)
        }
    }

    @JavascriptInterface
    fun sendMoengageEvent(eventName: String, jsonProp: String?) {
        activity?.runOnUiThread {
            listener?.sendMoengageEvent(eventName, jsonProp)
        }
    }

    @JavascriptInterface
    fun onTokokoEvent(eventName: String, jsonProp: String?) {
        activity?.runOnUiThread {
            listener?.onTokokoEvent(eventName, jsonProp)
        }
    }

    @JavascriptInterface
    fun launchActivityForResult(requestType: String) {
        activity?.runOnUiThread {
            listener?.launchActivityForResult(requestType)
        }
    }

    @JavascriptInterface
    fun onQrisSubmit(result: String, bookId: String) {
        activity?.runOnUiThread {
            listener?.onQrisSubmit(result, bookId)
        }
    }

    @JavascriptInterface
    fun selectBankAccount(isForQris: Boolean, setQrisBank: Boolean) {
        activity?.runOnUiThread {
            listener?.selectBankAccount(isForQris, setQrisBank)
        }
    }

    @JavascriptInterface
    fun selectQrisBankAccount(bookId: String) {
        activity?.runOnUiThread {
            listener?.selectQrisBankAccount(bookId)
        }
    }

    @JavascriptInterface
    fun addQrisBankAccount(bookId: String, addingFor: String, addingNewBank: Boolean) {
        activity?.runOnUiThread {
            listener?.addQrisBankAccount(bookId, addingFor, addingNewBank)
        }
    }

    @JavascriptInterface
    fun downloadFile(url: String, fileName: String) {
        activity?.runOnUiThread {
            listener?.downloadFile(url, fileName)
        }
    }

    @JavascriptInterface
    fun getAppVersion() {
        activity?.runOnUiThread {
            listener?.getAppVersion()
        }
    }

    @JavascriptInterface
    fun openKeyboard() {
        activity?.runOnUiThread {
            listener?.openKeyboard()
        }
    }

    @JavascriptInterface
    fun hideKeyboard() {
        activity?.runOnUiThread {
            listener?.hideKeyboard()
        }
    }

    @JavascriptInterface
    fun redirectToPaymentDetail(orderId:String, paymentType:String, isSuccess:Boolean) {
        activity?.runOnUiThread {
            listener?.redirectToPaymentDetail(orderId, paymentType, isSuccess)
        }
    }

    @JavascriptInterface
    fun redirectPaymentDetail(orderId: String, paymentType: String, isSuccess: Boolean, message: String, isFromAssistPage: Boolean) {
        activity?.runOnUiThread {
            listener?.redirectPaymentDetail(orderId, paymentType, isSuccess, message, isFromAssistPage)
        }
    }

    @JavascriptInterface
    fun openContactBook() {
        activity?.runOnUiThread {
            listener?.openContactBook()
        }
    }

    @JavascriptInterface
    fun getBnplBookId(): String {
       return listener?.getBnplBookId()!!
    }

    @JavascriptInterface
    fun getBnplBookName(): String {
        return listener?.getBnplBookName()!!
    }

    @JavascriptInterface
    fun setFeatureShown(id: String = "") {
        activity?.runOnUiThread {
            listener?.setFeatureShown(id)
        }
    }

    @JavascriptInterface
    fun isFeatureShown(id: String = ""): String {
        return listener?.isFeatureShown(id)!!
    }

    @JavascriptInterface
    fun getBookId(): String? {
        return listener?.getBWBookId()
    }

    @JavascriptInterface
    fun getBookName(): String? {
        return listener?.getBWBookName()
    }

    @JavascriptInterface
    fun getSessionValueByKey(id: String = ""): String? {
        return listener?.getSessionValueByKey(id)!!
    }

    @JavascriptInterface
    fun openCustomTab(url: String = "") {
        listener?.openCustomTab(url)
    }

    @JavascriptInterface
    fun openBookUpdateWithUseCase(bookId: String, useCase: String) {
        listener?.openBookUpdateWithUseCase(bookId, useCase)
    }

    @JavascriptInterface
    fun openBookUpdate(bookId: String) {
        listener?.openBookUpdate(bookId)
    }

    @JavascriptInterface
    fun trackEvent(
        eventName: String,
        jsonProp: String?,
        amplitude: Boolean,
        cleverTap: Boolean,
        firebase: Boolean,
        appsFlyer: Boolean,
    ) {
        activity?.runOnUiThread {
            listener?.trackEvent(
                eventName,
                jsonProp,
                amplitude,
                cleverTap,
                firebase,
                appsFlyer,
                null
            )
        }
    }

    @JavascriptInterface
    fun trackEvent(
        eventName: String,
        jsonProp: String?,
        amplitude: Boolean,
        cleverTap: Boolean,
        firebase: Boolean,
        appsFlyer: Boolean,
        tiktokEventName: String,
    ) {
        activity?.runOnUiThread {
            listener?.trackEvent(
                eventName,
                jsonProp,
                amplitude,
                cleverTap,
                firebase,
                appsFlyer,
                tiktokEventName,
            )
        }
    }

    @JavascriptInterface
    fun fetchLocationAndImage(imageType: String) {
        listener?.fetchLocationAndImage(imageType)
    }

    @JavascriptInterface
    fun getAppealBankAccount() {
        listener?.getAppealBankAccount()
    }

    @JavascriptInterface
    fun fetchAddress() {
        listener?.fetchAddress()
    }

    @JavascriptInterface
    fun getPhoneNumber(): String? {
        return listener?.getPhoneNumber()
    }

    @JavascriptInterface
    fun getSharedPrefValue(id: String = "", type: String = ""): String? {
        return listener?.getSharedPrefValue(id, type)
    }

    @JavascriptInterface
    fun redirectToDedicatedLoanBook() {
        listener?.redirectToDedicatedLoanBook()
    }

    @JavascriptInterface
    fun lockRotation(orientation: String) {
        listener?.lockRotation(orientation)
    }

    @JavascriptInterface
    fun onTimerFinish() {
        activity?.runOnUiThread {
            listener?.timerFinished()
        }
    }

    @JavascriptInterface
    fun onTrackEvent(eventName: String = "", eventProp: String = "") {
        activity?.runOnUiThread {
            listener?.trackEvent()
        }
    }

    @JavascriptInterface
    fun trackUserProperty(propName: String = "", propValue: String = "") {
        activity?.runOnUiThread {
            listener?.trackUserProperty(propName, propValue)
        }
    }

    @JavascriptInterface
    fun getBWAppToken(): String? {
        return listener?.getBWAppToken()
    }

    @JavascriptInterface
    fun getBWUserId(): String? {
        return listener?.getBWUserId()
    }

    @JavascriptInterface
    fun getAppVersionCode(): String? {
        return listener?.getAppVersionCode()
    }

    @JavascriptInterface
    fun getBWAppVersionName(): String? {
        return listener?.getBWAppVersionName()
    }

    @JavascriptInterface
    fun isSaldoActivated(): String? {
        return listener?.isSaldoActivated()
    }

    @JavascriptInterface
    fun getBWEntryPoint(): String? {
        return listener?.getBWEntryPoint()
    }

    @JavascriptInterface
    fun openBWActivity(activity: String = "", parameter: String, title: String) {
        listener?.openBWActivity(activity, parameter, title)
    }

    @JavascriptInterface
    fun openWhatsappWithMessage(phoneNumber: String = "", message: String = "") {
        listener?.openWhatsappWithMessage(phoneNumber, message)
    }

    @JavascriptInterface
    fun copyToClipboard(text: String = "", toastText:String = "Teks Tersalin!") {
        listener?.copyToClipboard(text, toastText)
    }

    @JavascriptInterface
    fun shareCaller() {
        listener?.shareCalled()
    }

    @JavascriptInterface
    fun fetchRemoteConfig(key: String): String? {
        return listener?.fetchRemoteConfig(key)
    }

    @JavascriptInterface
    fun openHelpDialog() {
        listener?.openHelpDialog()
    }

    @JavascriptInterface
    fun openBottomSheet(screenName: String, bottomSheetId: Int) {
        listener?.openBottomSheet(screenName, bottomSheetId)
    }

    @JavascriptInterface
    fun shareWithOpenTray(message: String?, phoneNumber: String?) {
        activity?.runOnUiThread {
            listener?.shareWithOpenTray(message, phoneNumber)
        }
    }

    @JavascriptInterface
    fun startOtpVerification(phoneNumber: String, countryCode: String, useCase: String) {
        activity?.runOnUiThread {
            listener?.startOtpVerification(phoneNumber, countryCode, useCase)
        }
    }

    @JavascriptInterface
    fun startPhysicalVisit() {
        activity?.runOnUiThread {
            listener?.startPhysicalVisit()
        }
    }
    
    @JavascriptInterface
    fun fetchDeviceDetails(): String? {
        return listener?.fetchDeviceDetails()
    }

    @JavascriptInterface
    fun shareOnWhatsapp(phoneNumber: String, message: String, url: String) {
        activity?.runOnUiThread {
            listener?.shareOnWhatsapp(phoneNumber, message, url)
        }
    }

    @JavascriptInterface
    fun openWebview(url: String?) {
        activity?.runOnUiThread {
            listener?.openWebview(url)
        }
    }

    @JavascriptInterface
    fun redirectVidaSign(url: String?) {
        activity?.runOnUiThread {
            listener?.redirectVidaSign(url)
        }
    }

    @JavascriptInterface
    fun openDialog(
        title: String,
        message: String,
        positiveButtonType: String,
        negativeButtonType: String,
        activityUrl: String,
        parameter: String
    ) {
        activity?.runOnUiThread {
            listener?.openDialog(
                title,
                message,
                positiveButtonType,
                negativeButtonType,
                activityUrl,
                parameter
            )
        }
    }
}
