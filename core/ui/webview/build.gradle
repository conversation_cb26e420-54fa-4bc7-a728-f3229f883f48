plugins {
    id 'com.android.library'
    id 'kotlin-android'
    id 'kotlin-parcelize'
}

android {
    compileSdkVersion 34
    buildToolsVersion "30.0.2"

    buildFeatures {
        viewBinding true
    }
    defaultConfig {
        minSdkVersion 21
        targetSdkVersion 34
        versionCode 1
        versionName "1.0"
        resConfigs "en_US", "id_ID"
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }

    repositories {
        flatDir {
            dirs 'libs'
        }
    }

    dependencies {
        implementation(name: 'LightCompressor-1.2.3', ext: 'aar')
    }
}

dependencies {
    api project(':privypass')

    implementation 'com.github.mrmike:ok2curl:0.8.0'
    implementation "org.jetbrains.kotlin:kotlin-stdlib:1.6.20"
    implementation "androidx.lifecycle:lifecycle-extensions:2.2.0"
    implementation "androidx.lifecycle:lifecycle-runtime-ktx:2.4.1"
    implementation "androidx.lifecycle:lifecycle-viewmodel-ktx:2.2.0"
    implementation "androidx.core:core-ktx:1.6.0"
    implementation "androidx.appcompat:appcompat:1.2.0"
    implementation "com.google.android.material:material:1.2.0"
    implementation "androidx.camera:camera-camera2:1.1.0-beta02"
    implementation "androidx.camera:camera-lifecycle:1.1.0-beta02"
    implementation "androidx.camera:camera-view:1.1.0-beta02"
    implementation 'androidx.camera:camera-video:1.1.0-beta02'
    implementation "androidx.constraintlayout:constraintlayout:2.0.4"
    implementation 'androidx.localbroadcastmanager:localbroadcastmanager:1.1.0'

    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-core:1.4.3"
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-android:1.4.3"

    implementation "com.github.bumptech.glide:glide:4.9.0"
    implementation "com.github.bumptech.glide:compiler:4.9.0"

    implementation "com.squareup.retrofit2:retrofit:2.9.0"
    implementation "com.squareup.retrofit2:converter-gson:2.9.0"
    implementation "com.squareup.okhttp3:okhttp:4.8.1"
    implementation "com.squareup.okhttp3:logging-interceptor:4.8.1"
    implementation "androidx.browser:browser:1.3.0"
    implementation 'id.zelory:compressor:3.0.1'
    implementation 'com.google.android.gms:play-services-location:21.0.1'
}


configurations.all {
    resolutionStrategy {
        force 'androidx.core:core:1.6.0'
    }
}